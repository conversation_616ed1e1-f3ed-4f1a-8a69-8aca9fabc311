__pycache__
.env

/litellm

pipelines/*
!pipelines/.gitignore
!pipelines/azure_openai_GPT35Turbo_pipeline.py
!pipelines/azure_openai_GPT4Omni_pipeline.py
!pipelines/bedrock_claude_haiku35_pipeline.py
!pipelines/bedrock_claude_haiku35_pipeline_mock.py
!pipelines/bedrock_claude_instant_pipeline.py
!pipelines/bedrock_claude_sonnet35_pipeline.py
!pipelines/bedrock_claude_sonnet35_v2_pipeline.py
!pipelines/bedrock_claude_sonnet37_pipeline.py
!pipelines/bedrock_llama32_11b_pipeline.py
!pipelines/rate_limit_filter_pipeline.py
!pipelines/google_vertex_manifold_pipeline.py
!pipelines/azure_openai_gpt4o_pipeline.py
!pipelines/azure_openai_gpt41_mini_pipeline.py
!pipelines/bedrock_claude_opus4_pipeline.py
!pipelines/bedrock_claude_sonnet4_pipeline.py
!pipelines/bedrock_llama4_maverick_pipeline.py
.DS_Store

.venv
venv/

.env
