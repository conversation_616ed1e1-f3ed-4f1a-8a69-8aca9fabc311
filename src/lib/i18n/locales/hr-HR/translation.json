{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' ili '-1' za bez isteka.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(npr. `sh webui.sh --api`)", "(latest)": "(najnovije)", "{{ models }}": "{{ modeli }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Razgo<PERSON><PERSON> k<PERSON> {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} Backend je potreban", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Model zadatka koristi se pri izvođenju zadataka kao što su generiranje naslova za razgovore i upite za pretraživanje weba", "a user": "korisnik", "About": "O aplikaciji", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Aktivni korisnici", "Add": "<PERSON><PERSON><PERSON>", "Add a model ID": "", "Add a short description about what this model does": "Dodajte kratak opis funkcija ovog modela", "Add a tag": "<PERSON><PERSON><PERSON>", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "<PERSON><PERSON><PERSON> prompt", "Add Files": "<PERSON><PERSON><PERSON>", "Add Group": "", "Add Memory": "<PERSON><PERSON><PERSON> memoriju", "Add Model": "Dodaj model", "Add Reaction": "", "Add Tag": "", "Add Tags": "<PERSON><PERSON><PERSON>", "Add text content": "", "Add User": "<PERSON><PERSON><PERSON>", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Podešavanje će se primijeniti univerzalno na sve korisnike.", "admin": "administrator", "Admin": "Admin", "Admin Panel": "<PERSON><PERSON>", "Admin Settings": "<PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "Napredni parametri", "Advanced Params": "Napredni parametri", "All Documents": "Svi dokumenti", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Dopusti brisanje razgovora", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "<PERSON><PERSON><PERSON>lo<PERSON> glasove", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "Već imate račun?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "asistent", "and": "i", "and {{COUNT}} more": "", "and create a new shared link.": "i stvorite novu dijeljenu vezu.", "API Base URL": "Osnovni URL API-ja", "API Key": "API ključ", "API Key created.": "API ključ je stvoren.", "API Key Endpoint Restrictions": "", "API keys": "API ključevi", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "<PERSON><PERSON><PERSON><PERSON>", "Archive": "<PERSON><PERSON><PERSON>", "Archive All Chats": "Arhivirajte sve razgovore", "Archived Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Jeste li sigurni?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "Prilo<PERSON>i da<PERSON>", "Attribute for Username": "", "Audio": "Audio", "August": "<PERSON><PERSON><PERSON>", "Authenticate": "", "Auto-Copy Response to Clipboard": "Automatsko kopiranje odgovora u međuspremnik", "Auto-playback response": "Automatska reprodukcija odgovora", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 osnovni URL", "AUTOMATIC1111 Base URL is required.": "Potreban je AUTOMATIC1111 osnovni URL.", "Available list": "", "available!": "dostupno!", "Azure AI Speech": "", "Azure Region": "", "Back": "Natrag", "Bad": "", "Bad Response": "Loš odgovor", "Banners": "<PERSON><PERSON>", "Base Model (From)": "Osnovni model (Od)", "Batch Size (num_batch)": "", "before": "prije", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Brave tražilica - API ključ", "By {{name}}": "", "Bypass SSL verification for Websites": "Zaobiđi SSL provjeru za web stranice", "Call": "Poziv", "Call feature is not supported when using Web STT engine": "Značajka poziva nije podržana kada se koristi Web STT mehanizam", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Ot<PERSON>ži", "Capabilities": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Capture": "", "Certificate Path": "", "Change Password": "Promijeni lo<PERSON>", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Razgovor", "Chat Background Image": "", "Chat Bubble UI": "Razgovor - Bubble UI", "Chat Controls": "", "Chat direction": "Razgovor - smijer", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Razgovori", "Check Again": "Provjeri ponovo", "Check for updates": "Provjeri za ažuriranja", "Checking for updates...": "<PERSON>v<PERSON>avam <PERSON>...", "Choose a model before saving...": "Odaberite model prije spremanja...", "Chunk Overlap": "Preklapanje dijelova", "Chunk Params": "Parametri <PERSON>", "Chunk Size": "Veličina di<PERSON>la", "Ciphers": "", "Citation": "Citiranje", "Clear memory": "<PERSON>č<PERSON>i memoriju", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Kliknite ovdje za pomoć.", "Click here to": "Kliknite ovdje za", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Kliknite ovdje za odabir", "Click here to select a csv file.": "Kliknite ovdje da odaberete csv datoteku.", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "kliknite ovdje.", "Click on the user role button to change a user's role.": "Kliknite na gumb uloge korisnika za promjenu uloge korisnika.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "<PERSON><PERSON><PERSON><PERSON>", "Close": "Zatvori", "Code execution": "", "Code formatted successfully": "", "Collection": "Kolekcija", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI osnovni URL", "ComfyUI Base URL is required.": "Potreban je ComfyUI osnovni URL.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Naredba", "Completions": "", "Concurrent Requests": "Istodobni zahtjevi", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "Potvrdite lozinku", "Confirm your action": "", "Confirm your new password": "", "Connections": "Povez<PERSON><PERSON>", "Contact Admin for WebUI Access": "Kontaktirajte admina za WebUI pristup", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Content Extraction": "", "Context Length": "Dužina konteksta", "Continue Response": "Nastavi odgovor", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "URL dijeljenog razgovora kopiran u međuspremnik!", "Copied to clipboard": "", "Copy": "<PERSON><PERSON><PERSON>", "Copy last code block": "<PERSON><PERSON><PERSON>adnji blok koda", "Copy last response": "<PERSON><PERSON><PERSON>ad<PERSON> odgovor", "Copy Link": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "", "Copying to clipboard was successful!": "Kopiranje u međuspremnik je uspješno!", "Create": "", "Create a knowledge base": "", "Create a model": "Izradite model", "Create Account": "Stvori račun", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Stvori novi ključ", "Create new secret key": "Stvori novi tajni ključ", "Created at": "<PERSON><PERSON><PERSON>", "Created At": "<PERSON><PERSON><PERSON>", "Created by": "", "CSV Import": "", "Current Model": "Trenutni model", "Current Password": "<PERSON><PERSON><PERSON><PERSON>", "Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dark": "Tamno", "Database": "Baza podataka", "December": "Prosinac", "Default": "Zadano", "Default (Open AI)": "", "Default (SentenceTransformers)": "Zadano (SentenceTransformers)", "Default Model": "Zadani model", "Default model updated": "Zadani model <PERSON><PERSON><PERSON><PERSON>", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Zadani prijedlozi prompta", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Zadana korisnička uloga", "Delete": "Izbriši", "Delete a model": "Izbriši model", "Delete All Chats": "Izbriši sve razgovore", "Delete All Models": "", "Delete chat": "Izbriši razgovor", "Delete Chat": "Izbriši razgovor", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "iz<PERSON>ši ovu vezu", "Delete tool?": "", "Delete User": "Izbriši korisnika", "Deleted {{deleteModelTag}}": "Izbrisan {{deleteModelTag}}", "Deleted {{name}}": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Opis", "Disabled": "", "Discover a function": "", "Discover a model": "Otkrijte model", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prompt", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, preuzmite i istražite prilagođene prompte", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "O<PERSON>k<PERSON>j<PERSON>, preuzmite i istražite unaprijed postavljene modele", "Dismissible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "Prikaži korisničko ime umjesto Vas u razgovoru", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Dokument", "Documentation": "Dokumentacija", "Documents": "Dokumenti", "does not make any external connections, and your data stays securely on your locally hosted server.": "ne uspostavlja vanj<PERSON> veze, a vaši podaci ostaju sigurno na vašem lokalno hostiranom poslužitelju.", "Don't have an account?": "<PERSON><PERSON><PERSON> ra<PERSON>?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "<PERSON>uzimanje", "Download canceled": "Preuzimanje o<PERSON>zano", "Download Database": "<PERSON>uz<PERSON> bazu podataka", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Spustite bilo koje datoteke ovdje za dodavanje u razgovor", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "npr. '30s','10m'. Važeće vremenske jedinice su 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "<PERSON><PERSON><PERSON>", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "<PERSON><PERSON><PERSON>", "Edit User Group": "", "ElevenLabs": "", "Email": "Email", "Embark on adventures": "", "Embedding Batch Size": "Embedding - Veličina batch-a", "Embedding Model": "Embedding model", "Embedding Model Engine": "Embedding model pogon", "Embedding model set to \"{{embedding_model}}\"": "Embedding model <PERSON><PERSON><PERSON><PERSON><PERSON> na \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Omogući zajedničko korištenje zajednice", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Omogući nove prijave", "Enable Web Search": "Omogući pretraživanje weba", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Provjerite da vaša CSV datoteka uključuje 4 stupca u ovom redoslijedu: Name, Email, Password, Role.", "Enter {{role}} message here": "Unesite {{role}} poruku ovdje", "Enter a detail about yourself for your LLMs to recall": "Unesite pojedinosti o sebi da bi učitali memoriju u LLM", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Unesite Brave Search API ključ", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Unesite preklapanje dijelova", "Enter Chunk Size": "Unesite veličinu dijela", "Enter description": "", "Enter Github Raw URL": "Unesite Github sirovi URL", "Enter Google PSE API Key": "Unesite Google PSE API ključ", "Enter Google PSE Engine Id": "Unesite ID Google PSE motora", "Enter Image Size (e.g. 512x512)": "Unesite veličinu slike (npr. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Unesite kodove jezika", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Unesite oznaku modela (npr. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Unesite broj koraka (npr. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Unesite o<PERSON>", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Unesite URL upita Searxng", "Enter Seed": "", "Enter Serper API Key": "Unesite Serper API ključ", "Enter Serply API Key": "Unesite Serply API ključ", "Enter Serpstack API Key": "Unesite Serpstack API ključ", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Unesite sekvencu zaustavljan<PERSON>", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Unesite Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Unesite URL (npr. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Unesite URL (npr. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Unesite svoj email", "Enter Your Full Name": "Unesite svoje puno ime", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "Unesite svoju lozinku", "Enter your prompt": "", "Enter Your Role": "Unesite svoju ulogu", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Greška", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Eksperimentalno", "Explore the cosmos": "", "Export": "Izvoz", "Export All Archived Chats": "", "Export All Chats (All Users)": "Izvoz svih razgovora (svi korisnici)", "Export chat (.json)": "Izvoz četa (.json)", "Export Chats": "Izvoz razgovora", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "Izvoz modela", "Export Presets": "", "Export Prompts": "Izvoz prompta", "Export to CSV": "", "Export Tools": "<PERSON>z<PERSON>z alata", "External Models": "Vanjski modeli", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Neuspješno stvaranje API ključa.", "Failed to read clipboard contents": "Neuspješno čitanje sadržaja međuspremnika", "Failed to save models configuration": "", "Failed to update settings": "Greška kod ažuriranja postavki", "February": "Vel<PERSON>ča", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "<PERSON><PERSON><PERSON>", "File not found.": "Datoteka nije pronađena.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Otkriveno krivotvorenje otisaka prstiju: Nemoguće je koristiti inicijale kao avatar. Postavljanje na zadanu profilnu sliku.", "Fluidly stream large external response chunks": "Glavno strujanje velikih vanjskih dijelova odgovora", "Focus chat input": "Fokusiraj unos razgovora", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "Kazna za učestalost", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "Općenito", "General Settings": "<PERSON><PERSON><PERSON>", "Generate Image": "<PERSON><PERSON><PERSON><PERSON> sliku", "Generating search query": "Generiranje upita za pretraživanje", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "Dobar odgovor", "Google Drive": "", "Google PSE API Key": "Google PSE API ključ", "Google PSE Engine Id": "ID Google PSE modula", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "nema razgovora.", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON><PERSON><PERSON>", "Host": "", "How can I help you today?": "<PERSON>ko vam mogu pomoći danas?", "How would you rate this response?": "", "Hybrid Search": "Hibridna pretraga", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Generiranje slika (eksperimentalno)", "Image Generation Engine": "Stroj za generiranje slika", "Image Max Compression Size": "", "Image Settings": "Postavke slike", "Images": "Slike", "Import Chats": "Uvoz razgovora", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "Uvoz modela", "Import Presets": "", "Import Prompts": "Uvoz prompta", "Import Tools": "U<PERSON>z alata", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "Uključite zastavicu `--api` prilikom pokretanja stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Informacije", "Input commands": "Unos naredbi", "Install from Github URL": "Instaliraj s Github URL-a", "Instant Auto-Send After Voice Transcription": "Trenutačno automatsko slanje nakon glasovne transkripcije", "Interface": "Sučelje", "Invalid file format.": "", "Invalid Tag": "Nevažeća oz<PERSON>", "is typing...": "", "January": "Siječanj", "Jina API Key": "", "join our Discord for help.": "pridružite se našem Discordu za pomoć.", "JSON": "JSON", "JSON Preview": "JSON pretpregled", "July": "<PERSON><PERSON><PERSON>", "June": "Lipanj", "JWT Expiration": "Isticanje JWT-a", "JWT Token": "JWT token", "Kagi Search API Key": "", "Keep Alive": "<PERSON><PERSON><PERSON><PERSON><PERSON> živim", "Key": "", "Keyboard shortcuts": "Tipkovnički prečaci", "Knowledge": "Znanje", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "<PERSON><PERSON><PERSON>", "Last Active": "Zadnja aktivnost", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "<PERSON><PERSON><PERSON><PERSON>", "Listening...": "S<PERSON>šam...", "Local": "", "Local Models": "Lokalni modeli", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Izradio OpenWebUI Community", "Make sure to enclose them with": "Provjerite da ih zatvorite s", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "Upravljaj", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Upravljanje cjevovodima", "March": "<PERSON><PERSON><PERSON><PERSON>", "Max Tokens (num_predict)": "<PERSON><PERSON><PERSON><PERSON> broj <PERSON> (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maksimalno 3 modela se mogu preuzeti istovremeno. Pokušajte ponovo kasnije.", "May": "Svibanj", "Memories accessible by LLMs will be shown here.": "O<PERSON>d<PERSON> će biti prikazana memorija kojoj mogu pristupiti LLM-ovi.", "Memory": "Memorija", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Poruke koje pošaljete nakon stvaranja veze neće se dijeliti. Korisnici s URL-om moći će vidjeti zajednički chat.", "Min P": "", "Minimum Score": "Minimalna o<PERSON>na", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{modelName}}' je <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' je već u redu za preuzimanje.", "Model {{modelId}} not found": "Model {{modelId}} nije pronađen", "Model {{modelName}} is not vision capable": "Model {{modelName}} ne <PERSON>ita vizualne impute", "Model {{name}} is now {{status}}": "Model {{name}} sada je {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "<PERSON><PERSON><PERSON><PERSON><PERSON> put datotečnog sustava modela. Krat<PERSON> ime modela je potrebno za ažuriranje, nije moguće nastaviti.", "Model Filtering": "", "Model ID": "ID modela", "Model IDs": "", "Model Name": "", "Model not selected": "<PERSON> nije o<PERSON>n", "Model Params": "Model parametri", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>a", "Models": "<PERSON><PERSON>", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON><PERSON><PERSON>", "Name": "Ime", "Name your knowledge base": "", "New Chat": "Novi razgovor", "New folder": "", "New Password": "Nova lozinka", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "<PERSON><PERSON> rezultata", "No search query generated": "Nije generiran upit za pretraživanje", "No source available": "<PERSON>ema dos<PERSON>pnog izvora", "No users were found.": "", "No valves to update": "", "None": "<PERSON><PERSON><PERSON>", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Napomena: <PERSON><PERSON> post<PERSON> o<PERSON>, pretraga će vratiti samo dokumente s ocjenom većom ili jednakom minimalnoj ocjeni.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Obavijesti", "November": "<PERSON><PERSON><PERSON>", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "", "October": "Listopad", "Off": "Isključeno", "Okay, Let's Go!": "U redu, idemo!", "OLED Dark": "OLED Tamno", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API je onemogućen", "Ollama API settings updated": "", "Ollama Version": "Ollama verzija", "On": "Uključeno", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Samo alfanumerički znakovi i crtice su dopušteni u naredbenom nizu.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ups! Izgleda da je URL nevažeći. Molimo provjerite ponovno i pokušajte ponovo.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ups! Koristite nepodržanu metodu (samo frontend). Molimo poslužite WebUI s backend-a.", "Open in full screen": "", "Open new chat": "Otvorite novi razgovor", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API konfiguracija", "OpenAI API Key is required.": "Potreban je OpenAI API ključ.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "Potreban je OpenAI URL/ključ.", "or": "ili", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "Lozinka", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF dokument (.pdf)", "PDF Extract Images (OCR)": "PDF izdvajanje slika (OCR)", "pending": "u tijeku", "Permission denied when accessing media devices": "Dopuštenje je odbijeno prilikom pristupa medijskim uređajima", "Permission denied when accessing microphone": "Dopuštenje je odbijeno prilikom pristupa mikrofonu", "Permission denied when accessing microphone: {{error}}": "<PERSON><PERSON><PERSON> mikrofo<PERSON> odbi<PERSON>n: {{error}}", "Permissions": "", "Personalization": "Prilagodba", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "Cjevovodi", "Pipelines Not Detected": "", "Pipelines Valves": "Ventili za cjevovode", "Plain text (.txt)": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>t (.txt)", "Playground": "Igrališ<PERSON>", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Prethodnih 30 dana", "Previous 7 days": "Prethodnih 7 dana", "Profile Image": "Profilna slika", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (npr. <PERSON><PERSON> mi zanimljivost o Rimskom carstvu)", "Prompt Content": "<PERSON><PERSON><PERSON><PERSON> prompta", "Prompt created successfully": "", "Prompt suggestions": "Prijedlozi prompta", "Prompt updated successfully": "", "Prompts": "Prompti", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Povucite \"{{searchValue}}\" s Ollama.com", "Pull a model from Ollama.com": "Povucite model s Ollama.com", "Query Generation Prompt": "", "Query Params": "Parametri upita", "RAG Template": "RAG predložak", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "<PERSON><PERSON><PERSON>", "Record voice": "Snimanje glasa", "Redirecting you to OpenWebUI Community": "Preusmjeravanje na OpenWebUI zajednicu", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Nazivajte se \"Korisnik\" (npr. \"Korisnik uči španjolski\")", "References from": "", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON>iraj", "Release Notes": "Bilješke o izdanju", "Relevance": "", "Remove": "Ukloni", "Remove Model": "Ukloni model", "Rename": "<PERSON><PERSON><PERSON><PERSON>", "Reorder Models": "", "Repeat Last N": "Ponovi zadnjih N", "Reply in Thread": "", "Request Mode": "<PERSON><PERSON><PERSON>", "Reranking Model": "Model za p<PERSON><PERSON><PERSON>", "Reranking model disabled": "Model za pono<PERSON><PERSON>", "Reranking model set to \"{{reranking_model}}\"": "Model za ponovno rangiranje postavljen na \"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "Poništi upload direktorij", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "<PERSON><PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "Pokrenuto", "Save": "Sp<PERSON>i", "Save & Create": "Spremi i stvori", "Save & Update": "Spremi i ažuriraj", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Spremanje zapisnika razgovora izravno u pohranu vašeg preglednika više nije podržano. Molimo vas da odvojite trenutak za preuzimanje i brisanje zapisnika razgovora klikom na gumb ispod. Ne brinite, možete lako ponovno uvesti zapisnike razgovora u backend putem", "Scroll to bottom when switching between branches": "", "Search": "Pretraga", "Search a model": "Pretraži model", "Search Base": "", "Search Chats": "Pretraži razgovore", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "Pretražite modele", "Search options": "", "Search Prompts": "Pretraga prompta", "Search Result Count": "Broj rezultata pretraživanja", "Search the web": "", "Search Tools": "Alati za pretraživanje", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "Searxng URL upita", "See readme.md for instructions": "Pogledajte readme.md za upute", "See what's new": "Pogledajte što je novo", "Seed": "<PERSON><PERSON><PERSON>", "Select a base model": "Odabir osnovnog modela", "Select a engine": "Odaberite pogon", "Select a function": "", "Select a group": "", "Select a model": "Odaberite model", "Select a pipeline": "<PERSON><PERSON><PERSON> kanala", "Select a pipeline url": "Odabir URL-a kanala", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "Odaberite model", "Select only one model to call": "Odaberite samo jedan model za poziv", "Selected model(s) do not support image inputs": "Odabrani modeli ne podržavaju unose slika", "Semantic distance to query": "", "Send": "Pošalji", "Send a message": "", "Send a Message": "Pošaljite poruku", "Send message": "Pošalji poruku", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "<PERSON><PERSON><PERSON>", "Serper API Key": "Serper API ključ", "Serply API Key": "Serply API ključ", "Serpstack API Key": "Serpstack API API ključ", "Server connection verified": "Veza s poslužiteljem potvrđena", "Set as default": "<PERSON><PERSON> kao zadano", "Set CFG Scale": "", "Set Default Model": "<PERSON><PERSON> model", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Postavi model za embedding (npr. {{model}})", "Set Image Size": "<PERSON><PERSON> slike", "Set reranking model (e.g. {{model}})": "Postavi model za ponovno <PERSON> (npr. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "<PERSON><PERSON> k<PERSON>", "Set Task Model": "Postavite model z<PERSON><PERSON><PERSON>", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Postavi glas", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Postavke", "Settings saved successfully!": "Postavke su uspješno spremljene!", "Share": "<PERSON><PERSON><PERSON><PERSON>", "Share Chat": "Po<PERSON>je<PERSON> r<PERSON>", "Share to OpenWebUI Community": "Podijeli u OpenWebUI zajednici", "Show": "Pokaži", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "Po<PERSON><PERSON><PERSON> pre<PERSON>", "Show your support!": "", "Sign in": "<PERSON><PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON><PERSON><PERSON><PERSON>", "Sign up": "Registracija", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "<PERSON><PERSON><PERSON>", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Pogreška prepoznavanja govora: {{error}}", "Speech-to-Text Engine": "Stroj za prepoznavanje govora", "Stop": "", "Stop Sequence": "<PERSON><PERSON><PERSON><PERSON>", "Stream Chat Response": "", "STT Model": "STT model", "STT Settings": "STT postavke", "Success": "<PERSON><PERSON><PERSON><PERSON>", "Successfully updated.": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "Sustav", "System Instructions": "", "System Prompt": "<PERSON><PERSON><PERSON><PERSON> prompt", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "Temperatura", "Template": "Predložak", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "Stroj za pretvorbu teksta u govor", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Hvala na povratnim informacijama!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Ocjena treba biti vrijednost između 0,0 (0%) i 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Razmišljam", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Ovo osigurava da su vaši vrijedni razgovori sigurno spremljeni u bazu podataka. Hvala vam!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Ovo je eksperimentalna značajka, možda neće funkcionirati prema očekivanjima i podložna je promjenama u bilo kojem trenutku.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Savjet: Až<PERSON>rajte više mjesta za varijable uzastopno pritiskom na tipku tab u unosu razgovora nakon svake zamjene.", "Title": "<PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "Na<PERSON>lov (npr. <PERSON><PERSON> mi zanimljivost)", "Title Auto-Generation": "Automatsko generiranje naslova", "Title cannot be an empty string.": "Naslov ne može biti prazni niz.", "Title Generation Prompt": "Prompt za generiranje naslova", "TLS": "", "To access the available model names for downloading,": "Za pristup dostupnim nazivima modela za preuzimanje,", "To access the GGUF models available for downloading,": "Za pristup GGUF modelima dostupnim za preuzimanje,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Za pristup WebUI-u obratite se administratoru. Administratori mogu upravljati statusima korisnika s <PERSON><PERSON> panela.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "<PERSON><PERSON>", "Toggle settings": "Prebaci postavke", "Toggle sidebar": "Prebaci boč<PERSON> traku", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "<PERSON><PERSON>", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "<PERSON>i s pristupom <PERSON>?", "TTS Model": "TTS model", "TTS Settings": "TTS postavke", "TTS Voice": "TTS glas", "Type": "Tip", "Type Hugging Face Resolve (Download) URL": "Upišite Hugging Face Resolve (Download) URL", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "Ažuriraj i kopiraj vezu", "Update for the latest features and improvements.": "", "Update password": "<PERSON><PERSON><PERSON><PERSON>", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "Učitaj GGUF model", "Upload directory": "", "Upload files": "", "Upload Files": "<PERSON><PERSON><PERSON><PERSON>", "Upload Pipeline": "Prijenos kanala", "Upload Progress": "Napredak učitavanja", "URL": "", "URL Mode": "URL način", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "<PERSON><PERSON><PERSON>", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "korisnik", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "<PERSON><PERSON><PERSON><PERSON>", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "Is<PERSON><PERSON><PERSON>", "Valid time units:": "Važeće vremenske jedinice:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "varijabla", "variable to have them replaced with clipboard content.": "varijabla za zamjenu sadrž<PERSON>em međuspremnika.", "Version": "Verzija", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "Upozorenje", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Upozorenje: <PERSON><PERSON> ili promijenite svoj model za umetan<PERSON>, morat ćete ponovno uvesti sve dokumente.", "Web": "Web", "Web API": "Web API", "Web Loader Settings": "Postavke web učitavanja", "Web Search": "Internet pretraga", "Web Search Engine": "Web tražilica", "Web Search Query Generation": "", "Webhook URL": "URL webkuke", "WebUI Settings": "WebUI postavke", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Što je novo u", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "Whisper (lokalno)", "Widescreen Mode": "<PERSON>d <PERSON> z<PERSON>lon<PERSON>", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Radna <PERSON>", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Napišite prijedlog prompta (npr. Tko si ti?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Napišite sažetak u 50 riječi koji sažima [temu ili ključnu riječ].", "Write something...": "", "Write your model template content here": "", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "Vi", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Možete personalizirati svoje interakcije s LLM-ima dodavanjem uspomena putem gumba 'Upravljanje' u nastavku, čineći ih korisnijima i prilagođenijima vama.", "You cannot upload an empty file.": "", "You have no archived conversations.": "<PERSON><PERSON><PERSON> r<PERSON>.", "You have shared this chat": "Podijelili ste ovaj razgovor", "You're a helpful assistant.": "<PERSON>i ste korisni asistent.", "Your account status is currently pending activation.": "Status vašeg računa trenutno čeka aktivaciju.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "YouTube", "Youtube Loader Settings": "YouTube postavke učitavanja"}