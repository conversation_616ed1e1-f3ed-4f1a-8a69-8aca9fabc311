{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' atau '-1' untuk tidak ada kedaluwarsa.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(contoh: `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(contoh: `sh webui.sh --api`)", "(latest)": "(terbaru)", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Obrolan {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} <PERSON><PERSON><PERSON><PERSON> Backend", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Model tugas digunakan saat melakukan tugas seperti membuat judul untuk obrolan dan kueri penelusuran web", "a user": "seorang pengguna", "About": "Tentang", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "Aktivasi Akun Tertunda", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Pengguna Aktif", "Add": "Tambah", "Add a model ID": "", "Add a short description about what this model does": "Tambahkan deskripsi singkat tentang apa yang dilakukan model ini", "Add a tag": "Menambah<PERSON> tag", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "Tambahkan prompt khusus", "Add Files": "Menambahkan File", "Add Group": "", "Add Memory": "Menambahkan Memori", "Add Model": "Tambahkan Model", "Add Reaction": "", "Add Tag": "", "Add Tags": "Tambahkan Tag", "Add text content": "", "Add User": "Tambah Pengguna", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Menyesuaikan pengaturan ini akan menerapkan perubahan secara universal ke semua pengguna.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Panel Admin", "Admin Settings": "<PERSON><PERSON><PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Admin memiliki akses ke semua alat setiap saat; pengguna memerlukan alat yang ditetapkan per model di ruang kerja.", "Advanced Parameters": "Parameter Lanju<PERSON>", "Advanced Params": "Parameter Lanju<PERSON>", "All Documents": "<PERSON><PERSON><PERSON>", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Izinkan Penghapusan Obrolan", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "Izinkan suara non-lokal", "Allow Temporary Chat": "", "Allow User Location": "Izinkan Lokasi Pengguna", "Allow Voice Interruption in Call": "Izinkan Gangguan Suara dalam Pangg<PERSON>n", "Allowed Endpoints": "", "Already have an account?": "Sudah memiliki akun?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "asisten", "and": "dan", "and {{COUNT}} more": "", "and create a new shared link.": "dan membuat tautan bersama baru.", "API Base URL": "URL Dasar API", "API Key": "Kunci API", "API Key created.": "Kunci API dibuat.", "API Key Endpoint Restrictions": "", "API keys": "Kunci API", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "April", "Archive": "<PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "<PERSON><PERSON><PERSON><PERSON>", "Archived Chats": "<PERSON><PERSON><PERSON> yang <PERSON>ars<PERSON>", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "<PERSON><PERSON><PERSON><PERSON> Anda yakin?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "Lampirkan file", "Attribute for Username": "", "Audio": "Audio", "August": "<PERSON><PERSON><PERSON>", "Authenticate": "", "Auto-Copy Response to Clipboard": "Tangga<PERSON> ke Papan Klip", "Auto-playback response": "Respons pemutaran otomatis", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "URL Dasar AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 URL Dasar diperlukan.", "Available list": "", "available!": "tersedia!", "Azure AI Speech": "", "Azure Region": "", "Back": "Kembali", "Bad": "", "Bad Response": "Respons Buruk", "Banners": "Spanduk", "Base Model (From)": "<PERSON> (Dari)", "Batch Size (num_batch)": "<PERSON><PERSON><PERSON> Batch (num_batch)", "before": "sebelum", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Kunci API Pencarian Berani", "By {{name}}": "", "Bypass SSL verification for Websites": "Lewati verifikasi SSL untuk Situs Web", "Call": "Panggilan", "Call feature is not supported when using Web STT engine": "Fitur panggilan tidak didukung saat menggunakan mesin Web STT", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON>", "Capabilities": "Kemampuan", "Capture": "", "Certificate Path": "", "Change Password": "Ubah Kata Sandi", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "<PERSON><PERSON>lan", "Chat Background Image": "Gambar Latar Belakang Obrolan", "Chat Bubble UI": "UI Gelembung Obrolan", "Chat Controls": "", "Chat direction": "<PERSON><PERSON>", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "<PERSON><PERSON>lan", "Check Again": "<PERSON><PERSON><PERSON>", "Check for updates": "Memeriksa pembaruan", "Checking for updates...": "Memeriksa pembaruan...", "Choose a model before saving...": "<PERSON><PERSON>h model sebelum <PERSON>...", "Chunk Overlap": "Tumpang Tindih <PERSON>gan", "Chunk Params": "Parameter Po<PERSON>gan", "Chunk Size": "Ukuran <PERSON>gan", "Ciphers": "", "Citation": "Ku<PERSON><PERSON>", "Clear memory": "<PERSON><PERSON><PERSON><PERSON> memori", "click here": "", "Click here for filter guides.": "", "Click here for help.": "<PERSON>lik di sini untuk bantuan.", "Click here to": "Klik di sini untuk", "Click here to download user import template file.": "Klik di sini untuk mengunduh file templat impor pengguna.", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Klik di sini untuk memilih", "Click here to select a csv file.": "Klik di sini untuk memilih file csv.", "Click here to select a py file.": "Klik di sini untuk memilih file py.", "Click here to upload a workflow.json file.": "", "click here.": "<PERSON><PERSON> di sini.", "Click on the user role button to change a user's role.": "Klik tombol peran pengguna untuk mengubah peran pengguna.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "<PERSON><PERSON> menulis papan klip di<PERSON>lak. Periksa pengaturan peramban Anda untuk memberikan akses yang diperlukan.", "Clone": "Kloning", "Close": "<PERSON><PERSON><PERSON>", "Code execution": "", "Code formatted successfully": "Kode berhasil diformat", "Collection": "<PERSON><PERSON><PERSON><PERSON>", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL Dasar ComfyUI", "ComfyUI Base URL is required.": "URL Dasar ComfyUI diperlukan.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "<PERSON><PERSON><PERSON>", "Completions": "", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON>", "Configure": "", "Configure Models": "", "Confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm Password": "<PERSON>n<PERSON><PERSON><PERSON>", "Confirm your action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm your new password": "", "Connections": "Koneks<PERSON>", "Contact Admin for WebUI Access": "Hubungi Admin untuk Akses WebUI", "Content": "Konten", "Content Extraction": "", "Context Length": "Panjang Konteks", "Continue Response": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Continue with {{provider}}": "<PERSON><PERSON><PERSON><PERSON><PERSON> den<PERSON> {{penyedia}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "Menyalin URL obrolan bersama ke papan klip!", "Copied to clipboard": "", "Copy": "<PERSON><PERSON><PERSON>", "Copy last code block": "<PERSON>in blok kode terakhir", "Copy last response": "<PERSON><PERSON> tanggapan terakhir", "Copy Link": "<PERSON><PERSON>", "Copy to clipboard": "", "Copying to clipboard was successful!": "Penyalinan ke papan klip berhasil!", "Create": "", "Create a knowledge base": "", "Create a model": "Buat model", "Create Account": "Buat A<PERSON>n", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Buat kunci baru", "Create new secret key": "<PERSON><PERSON>t kunci rahasia baru", "Created at": "Dibuat di", "Created At": "Dibuat di", "Created by": "Dibuat oleh", "CSV Import": "Impor CSV", "Current Model": "Model <PERSON><PERSON>", "Current Password": "Kata Sandi Sa<PERSON> In<PERSON>", "Custom": "Kustom", "Dark": "<PERSON><PERSON><PERSON>", "Database": "Basis data", "December": "Desember", "Default": "<PERSON><PERSON><PERSON>", "Default (Open AI)": "", "Default (SentenceTransformers)": "Default (Pengubah Kalimat)", "Default Model": "<PERSON> De<PERSON>ult", "Default model updated": "Model default diperbarui", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "<PERSON><PERSON>", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "<PERSON><PERSON>", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete a model": "Menghapus model", "Delete All Chats": "<PERSON><PERSON><PERSON><PERSON>", "Delete All Models": "", "Delete chat": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "Delete Chat": "<PERSON><PERSON><PERSON><PERSON>", "Delete chat?": "<PERSON><PERSON><PERSON><PERSON> obrolan?", "Delete folder?": "", "Delete function?": "Fungsi hapus?", "Delete Message": "", "Delete prompt?": "<PERSON><PERSON><PERSON> ha<PERSON>?", "delete this link": "hapus tautan ini", "Delete tool?": "Hapus alat?", "Delete User": "<PERSON><PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "Menghapus {{deleteModelTag}}", "Deleted {{name}}": "Menghapus {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "<PERSON><PERSON><PERSON><PERSON>", "Disabled": "", "Discover a function": "<PERSON><PERSON><PERSON><PERSON> sebuah fungsi", "Discover a model": "<PERSON><PERSON><PERSON><PERSON> se<PERSON><PERSON> model", "Discover a prompt": "<PERSON><PERSON><PERSON>", "Discover a tool": "<PERSON><PERSON><PERSON><PERSON> alat", "Discover wonders": "", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan men<PERSON><PERSON><PERSON>i fungsi khusus", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, dan j<PERSON><PERSON>i prompt khusus", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> alat khusus", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> preset model", "Dismissible": "Tidak dapat digunakan", "Display": "", "Display Emoji in Call": "Menampilkan Emoji <PERSON>", "Display the username instead of You in the Chat": "Menampilkan nama pen<PERSON>una, bukan <PERSON> di O<PERSON>lan", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Dokumen", "Documentation": "Dokumentasi", "Documents": "Dokumen", "does not make any external connections, and your data stays securely on your locally hosted server.": "tidak membuat koneksi eksternal apa pun, dan data Anda tetap aman di server yang dihosting secara lokal.", "Don't have an account?": "Tidak memiliki akun?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "Se<PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON>", "Download canceled": "<PERSON><PERSON><PERSON>", "Download Database": "Unduh Basis Data", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Letakkan file apa pun di sini untuk ditambahkan ke percakapan", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "misalnya '30-an', '10m'. <PERSON><PERSON><PERSON> waktu yang valid adalah 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Edit", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "<PERSON>", "Edit User": "<PERSON>", "Edit User Group": "", "ElevenLabs": "", "Email": "Email", "Embark on adventures": "", "Embedding Batch Size": "Menyematkan Ukuran Batch", "Embedding Model": "Model Penyematan", "Embedding Model Engine": "Mesin Model Penyematan", "Embedding model set to \"{{embedding_model}}\"": "Model penyematan diatur ke \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Aktifkan Berbagi Komunitas", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Aktifkan Pendaftaran Baru", "Enable Web Search": "Aktifkan Pencarian Web", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Pastikan file CSV Anda menyertakan 4 kolom dengan urutan sebagai berikut: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON><PERSON> pesan {{role}} di sini", "Enter a detail about yourself for your LLMs to recall": "Masukkan detail tentang diri Anda untuk diingat oleh LLM Anda", "Enter api auth string (e.g. username:password)": "Masukkan string pengesahan API (misalnya nama pengguna: kata sandi)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Masukkan Kunci API Pencarian Berani", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Masukkan Tumpang Tindih Chunk", "Enter Chunk Size": "Masukkan <PERSON>", "Enter description": "", "Enter Github Raw URL": "Masukkan URL Mentah Github", "Enter Google PSE API Key": "Masukkan Kunci API Google PSE", "Enter Google PSE Engine Id": "Masukkan Id Mesin Google PSE", "Enter Image Size (e.g. 512x512)": "Masukkan Ukuran Gambar (mis. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "<PERSON><PERSON><PERSON><PERSON> kode bahasa", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Masukkan tag model (misalnya {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON><PERSON> (mis. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Masukkan Skor", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Masukkan URL Kueri Searxng", "Enter Seed": "", "Enter Serper API Key": "Masukkan Kunci API Serper", "Enter Serply API Key": "Masukkan Kunci API Serply", "Enter Serpstack API Key": "Masukkan Kunci API Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "<PERSON><PERSON><PERSON><PERSON> urutan be<PERSON>", "Enter system prompt": "", "Enter Tavily API Key": "Masukkan Kunci API Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Masukkan Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Masukkan URL (mis. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Masukkan URL (mis. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "<PERSON><PERSON><PERSON><PERSON>", "Enter Your Full Name": "<PERSON><PERSON><PERSON><PERSON>", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "<PERSON><PERSON><PERSON><PERSON>", "Enter your prompt": "", "Enter Your Role": "<PERSON><PERSON><PERSON><PERSON>", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "<PERSON><PERSON><PERSON>", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Per<PERSON><PERSON><PERSON>", "Explore the cosmos": "", "Export": "Ekspor", "Export All Archived Chats": "", "Export All Chats (All Users)": "Ekspor Semua Obrolan (Semua Pengguna)", "Export chat (.json)": "Ekspor obrolan (.json)", "Export Chats": "Ekspor Obrolan", "Export Config to JSON File": "", "Export Functions": "Fungsi Ekspor", "Export Models": "Model Ekspor", "Export Presets": "", "Export Prompts": "Perintah Ekspor", "Export to CSV": "", "Export Tools": "Alat Ekspor", "External Models": "<PERSON> Eksternal", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Gagal membuat API Key.", "Failed to read clipboard contents": "Gagal membaca konten papan klip", "Failed to save models configuration": "", "Failed to update settings": "<PERSON><PERSON>n", "February": "<PERSON><PERSON><PERSON>", "Feedback History": "", "Feedbacks": "", "File": "Berkas", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "Mode File", "File not found.": "File tidak ditemukan.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "Filter sekarang dinonaktifkan secara global", "Filter is now globally enabled": "Filter sekarang diaktifkan secara global", "Filters": "Filter", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Pemalsuan sidik jari terdeteksi: Tidak dapat menggunakan inisial sebagai avatar. Default ke gambar profil default.", "Fluidly stream large external response chunks": "<PERSON><PERSON><PERSON><PERSON> potongan respons eksternal yang besar dengan lancar", "Focus chat input": "Memfokuskan input obrolan", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "<PERSON><PERSON><PERSON>", "Format your variables using brackets like this:": "", "Frequency Penalty": "<PERSON><PERSON><PERSON>", "Function": "", "Function created successfully": "Fungsi berhasil dibuat", "Function deleted successfully": "<PERSON><PERSON><PERSON> ber<PERSON><PERSON>", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "<PERSON><PERSON><PERSON> ber<PERSON><PERSON>", "Functions": "<PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "Fungsi ber<PERSON>il diimpor", "General": "<PERSON><PERSON>", "General Settings": "<PERSON><PERSON><PERSON><PERSON>", "Generate Image": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Generating search query": "<PERSON><PERSON><PERSON>t kueri penel<PERSON>n", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Global", "Good Response": "Respons yang <PERSON>", "Google Drive": "", "Google PSE API Key": "Kunci API Google PSE", "Google PSE Engine Id": "Id Mesin Google PSE", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "tidak memiliki percakapan.", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "Bantuan", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Sembunyikan", "Host": "", "How can I help you today?": "Ada yang bisa saya bantu hari ini?", "How would you rate this response?": "", "Hybrid Search": "Pencar<PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Pembuatan Gambar (Eksperimental)", "Image Generation Engine": "Mesin Pembuat Gambar", "Image Max Compression Size": "", "Image Settings": "Pengaturan Gambar", "Images": "Gambar", "Import Chats": "<PERSON><PERSON><PERSON>", "Import Config from JSON File": "", "Import Functions": "<PERSON>gs<PERSON> Impor", "Import Models": "Model Impor", "Import Presets": "", "Import Prompts": "Petunjuk Impor", "Import Tools": "Alat Impor", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "Ser<PERSON>kan bendera `--api-auth` saat menjalankan stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Ser<PERSON>kan bendera `--api` saat men<PERSON>an stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Info", "Input commands": "<PERSON><PERSON><PERSON>", "Install from Github URL": "Instal dari URL Github", "Instant Auto-Send After Voice Transcription": "<PERSON><PERSON> Instan Setelah Transkripsi Suara", "Interface": "Antarmuka", "Invalid file format.": "", "Invalid Tag": "Tag tidak valid", "is typing...": "", "January": "<PERSON><PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "berga<PERSON><PERSON><PERSON> dengan Discord kami untuk mendapatkan bantuan.", "JSON": "JSON", "JSON Preview": "Prat<PERSON>jau JSON", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "JWT Expiration": "Kedaluwarsa JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep Alive": "<PERSON><PERSON><PERSON>", "Key": "", "Keyboard shortcuts": "Pintasan keyboard", "Knowledge": "<PERSON><PERSON><PERSON><PERSON>", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "Bahasa", "Last Active": "Terakhir Aktif", "Last Modified": "<PERSON><PERSON><PERSON>", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Mendengarkan", "Local": "", "Local Models": "<PERSON> Lokal", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Dibuat oleh Komunitas OpenWebUI", "Make sure to enclose them with": "<PERSON>ikan untuk melampirkannya dengan", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "Mengelola", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Mengelola Saluran Pipa", "March": "<PERSON><PERSON>", "Max Tokens (num_predict)": "<PERSON><PERSON> (num_prediksi)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maksimal 3 model dapat diunduh secara bersa<PERSON>. <PERSON><PERSON><PERSON> coba lagi nanti.", "May": "<PERSON>", "Memories accessible by LLMs will be shown here.": "<PERSON><PERSON>i yang dapat diakses oleh LLM akan ditampilkan di sini.", "Memory": "<PERSON><PERSON><PERSON>", "Memory added successfully": "<PERSON><PERSON><PERSON> be<PERSON><PERSON> di<PERSON>", "Memory cleared successfully": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "Memory deleted successfully": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "Memory updated successfully": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON> yang Anda kirim setelah membuat tautan tidak akan dibagikan. Pengguna yang memiliki URL tersebut akan dapat melihat obrolan yang dibagikan.", "Min P": "", "Minimum Score": "Skor Minimum", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH: mm", "MMMM DD, YYYY hh:mm:ss A": "MMMM DD, YYYY jj: mm: dd A", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{modelName}}' telah ber<PERSON>.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' sudah berada dalam antrean untuk diunduh.", "Model {{modelId}} not found": "Model {{modelId}} tidak ditemukan", "Model {{modelName}} is not vision capable": "Model {{modelName}} tidak dapat dilihat", "Model {{name}} is now {{status}}": "Model {{name}} sekarang menjadi {{status}}", "Model accepts image inputs": "", "Model created successfully!": "Model berhasil dibuat!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "<PERSON><PERSON><PERSON> sistem berkas model terde<PERSON>ks<PERSON>. <PERSON><PERSON> model dip<PERSON><PERSON><PERSON> untuk pem<PERSON>, tidak dapat dilanju<PERSON>.", "Model Filtering": "", "Model ID": "ID Model", "Model IDs": "", "Model Name": "", "Model not selected": "Model tidak dipilih", "Model Params": "Parameter Model", "Model Permissions": "", "Model updated successfully": "Model ber<PERSON><PERSON>", "Modelfile Content": "Konten File Model", "Models": "Model", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON><PERSON>", "Name": "<PERSON><PERSON>", "Name your knowledge base": "", "New Chat": "<PERSON><PERSON><PERSON>", "New folder": "", "New Password": "<PERSON><PERSON>", "new-channel": "", "No content found": "", "No content to speak": "Tidak ada konten untuk dibicarakan", "No distance available": "", "No feedbacks found": "", "No file selected": "Tidak ada file yang dipilih", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "Tidak ada hasil yang di<PERSON>ukan", "No search query generated": "Tidak ada permintaan pencarian yang dibuat", "No source available": "Tidak ada sumber yang tersedia", "No users were found.": "", "No valves to update": "Tidak ada katup untuk diperbarui", "None": "Tidak ada", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Catatan: <PERSON><PERSON> Anda menetapkan skor minimum, pencarian hanya akan mengembalikan dokumen dengan skor yang lebih besar atau sama dengan skor minimum.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Pemberitahuan", "November": "November", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "ID OAuth", "October": "Oktober", "Off": "<PERSON><PERSON>", "Okay, Let's Go!": "<PERSON><PERSON>, <PERSON><PERSON>!", "OLED Dark": "OLED Gelap", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API disabled": "API Ollama dinonaktifkan", "Ollama API settings updated": "", "Ollama Version": "<PERSON><PERSON><PERSON>", "On": "Aktif", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "<PERSON>ya karakter alfanumerik dan tanda hubung yang diizinkan dalam string perintah.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ups! Sepertinya URL tidak valid. Mohon periksa ulang dan coba lagi.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ups! Anda menggunakan metode yang tidak didukung (hanya untuk frontend). Silakan sajikan WebUI dari backend.", "Open in full screen": "", "Open new chat": "<PERSON><PERSON> obrolan baru", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Konfigurasi API OpenAI", "OpenAI API Key is required.": "Diperlukan Kunci API OpenAI.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "Diperlukan URL/Kunci OpenAI.", "or": "atau", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "<PERSON>a sandi", "Paste Large Text as File": "", "PDF document (.pdf)": "Dokumen PDF (.pdf)", "PDF Extract Images (OCR)": "Ekstrak Gambar PDF (OCR)", "pending": "tertunda", "Permission denied when accessing media devices": "<PERSON>zin ditolak saat mengakses perangkat media", "Permission denied when accessing microphone": "<PERSON><PERSON> ditolak saat mengakses mikrofon", "Permission denied when accessing microphone: {{error}}": "<PERSON><PERSON> ditolak saat mengakses mikrofon: {{error}}", "Permissions": "", "Personalization": "<PERSON><PERSON><PERSON>", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "Pipeline berhasil dihapus", "Pipeline downloaded successfully": "<PERSON><PERSON><PERSON> pipa ber<PERSON><PERSON>", "Pipelines": "Saluran pipa", "Pipelines Not Detected": "Saluran Pipa Tidak Terdeteksi", "Pipelines Valves": "<PERSON><PERSON>", "Plain text (.txt)": "<PERSON><PERSON> (.txt)", "Playground": "<PERSON><PERSON>", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "30 hari sebelumnya", "Previous 7 days": "7 hari sebelumnya", "Profile Image": "Gambar Profil", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "<PERSON><PERSON><PERSON><PERSON> (mis. Ceritakan sebuah fakta menarik tentang <PERSON>)", "Prompt Content": "<PERSON><PERSON><PERSON> yang <PERSON>", "Prompt created successfully": "", "Prompt suggestions": "<PERSON>n yang diminta", "Prompt updated successfully": "", "Prompts": "Prompt", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "<PERSON><PERSON> \"{{searchValue}}\" dari <PERSON>.com", "Pull a model from Ollama.com": "Tarik model dari <PERSON>.com", "Query Generation Prompt": "", "Query Params": "Parameter <PERSON>", "RAG Template": "Templat RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "Baca dengan Ke<PERSON>", "Record voice": "<PERSON><PERSON><PERSON> suara", "Redirecting you to OpenWebUI Community": "Mengarahkan Anda ke Komunitas OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Merujuk diri Anda sebagai \"Pengguna\" (misal<PERSON>, \"Pengguna sedang belajar bahasa Spanyol\")", "References from": "", "Refresh Token Expiration": "", "Regenerate": "Regenerasi", "Release Notes": "Catatan Rilis", "Relevance": "", "Remove": "Hapus", "Remove Model": "Hapus Model", "Rename": "Ganti nama", "Reorder Models": "", "Repeat Last N": "Ulangi N Terakhir", "Reply in Thread": "", "Request Mode": "<PERSON>", "Reranking Model": "Model Pemeringkatan Ulang", "Reranking model disabled": "Model pemeringkatan ulang dinonakt<PERSON>kan", "Reranking model set to \"{{reranking_model}}\"": "Model pemeringkatan diatur ke \"{{reranking_model}}\"", "Reset": "<PERSON><PERSON>", "Reset All Models": "", "Reset Upload Directory": "Setel Ulang Direktori Unggahan", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Notifikasi respons tidak dapat diaktifkan karena izin situs web telah ditolak. Silakan kunjungi pengaturan browser Anda untuk memberikan akses yang dip<PERSON>.", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "<PERSON><PERSON><PERSON><PERSON>", "Save": "Simpan", "Save & Create": "Simpan & Buat", "Save & Update": "Simpan & Perbarui", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Menyimpan log obrolan secara langsung ke penyimpanan browser Anda tidak lagi didukung. Mohon luangkan waktu sejenak untuk mengunduh dan menghapus log obrolan Anda dengan mengeklik tombol di bawah ini. <PERSON><PERSON>, <PERSON><PERSON> dapat dengan mudah mengimpor kembali log obrolan Anda ke backend melalui", "Scroll to bottom when switching between branches": "", "Search": "<PERSON><PERSON>", "Search a model": "Mencari model", "Search Base": "", "Search Chats": "<PERSON><PERSON>", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "<PERSON><PERSON><PERSON>", "Search Knowledge": "", "Search Models": "Cari Model", "Search options": "", "Search Prompts": "<PERSON><PERSON><PERSON>", "Search Result Count": "<PERSON><PERSON><PERSON>", "Search the web": "", "Search Tools": "Alat Pencarian", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "URL Kueri Pencarian Searxng", "See readme.md for instructions": "<PERSON>hat readme.md untuk instruksi", "See what's new": "<PERSON>hat apa yang baru", "Seed": "<PERSON><PERSON>", "Select a base model": "<PERSON><PERSON><PERSON> model dasar", "Select a engine": "<PERSON><PERSON><PERSON> mesin", "Select a function": "<PERSON><PERSON><PERSON><PERSON> fun<PERSON>i", "Select a group": "", "Select a model": "<PERSON><PERSON><PERSON> model", "Select a pipeline": "<PERSON><PERSON><PERSON> saluran pipa", "Select a pipeline url": "<PERSON>lih url saluran pipa", "Select a tool": "<PERSON><PERSON><PERSON> alat", "Select Engine": "", "Select Knowledge": "", "Select model": "<PERSON><PERSON><PERSON> model", "Select only one model to call": "<PERSON><PERSON><PERSON> hanya satu model untuk dipanggil", "Selected model(s) do not support image inputs": "Model yang dipilih tidak mendukung input gambar", "Semantic distance to query": "", "Send": "<PERSON><PERSON>", "Send a message": "", "Send a Message": "<PERSON><PERSON>", "Send message": "<PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "September", "Serper API Key": "Kunci API Serper", "Serply API Key": "Kunci API Serply", "Serpstack API Key": "Kunci API Serpstack", "Server connection verified": "Koneksi server diverifikasi", "Set as default": "Ditetapkan sebagai default", "Set CFG Scale": "", "Set Default Model": "Tetapkan Model Default", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Tetapkan model penyematan (mis. {{model}})", "Set Image Size": "Mengatur Ukuran Gambar", "Set reranking model (e.g. {{model}})": "Tetapkan model pemeringkatan ulang (mis. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Tetapkan Langkah", "Set Task Model": "Tetapkan Model Tugas", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON><PERSON>", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Settings saved successfully!": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>il disimpan!", "Share": "<PERSON><PERSON><PERSON>", "Share Chat": "Bagikan Obrolan", "Share to OpenWebUI Community": "Bagikan ke Komunitas OpenWebUI", "Show": "<PERSON><PERSON><PERSON><PERSON>", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "<PERSON><PERSON><PERSON><PERSON> di <PERSON>", "Show shortcuts": "<PERSON><PERSON><PERSON><PERSON> pin<PERSON>an", "Show your support!": "Tunjukkan dukungan Anda!", "Sign in": "<PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON><PERSON><PERSON>", "Sign up": "<PERSON><PERSON><PERSON>", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "Sumber", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Kesalahan pengenalan suara: {{error}}", "Speech-to-Text Engine": "<PERSON>sin Pengenal Ucapan ke Teks", "Stop": "", "Stop Sequence": "Hentikan Urutan", "Stream Chat Response": "", "STT Model": "Model STT", "STT Settings": "Pengaturan STT", "Success": "<PERSON><PERSON><PERSON><PERSON>", "Successfully updated.": "<PERSON><PERSON><PERSON><PERSON>.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "Sistem", "System Instructions": "", "System Prompt": "<PERSON><PERSON><PERSON><PERSON>", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "Ketuk untuk menyela", "Tavily API Key": "Kunci API Tavily", "Temperature": "<PERSON><PERSON>", "Template": "Templat", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "<PERSON><PERSON>", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "<PERSON><PERSON> kasih atas umpan balik <PERSON>a!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "<PERSON><PERSON> yang diberikan haruslah nilai antara 0,0 (0%) dan 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON>", "Thinking...": "<PERSON><PERSON><PERSON><PERSON>", "This action cannot be undone. Do you wish to continue?": "Tindakan ini tidak dapat dibatalkan. <PERSON><PERSON><PERSON>h Anda ingin melanjutkan?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Ini akan memastikan bahwa percakapan Anda yang berharga disimpan dengan aman ke basis data backend. <PERSON><PERSON> kasih!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Ini adalah fitur eksperimental, mungkin tidak berfungsi seperti yang diharapkan dan dapat berubah sewaktu-waktu.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "<PERSON>i akan men<PERSON>", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tips: <PERSON><PERSON><PERSON> beberapa slot variabel secara berurutan dengan menekan tombol tab di input obrolan setelah setiap penggantian.", "Title": "<PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON> (mi<PERSON><PERSON>, Ceritakan sebuah fakta menarik)", "Title Auto-Generation": "Pembuatan Ju<PERSON>", "Title cannot be an empty string.": "<PERSON><PERSON><PERSON> tidak boleh berupa string kosong.", "Title Generation Prompt": "<PERSON><PERSON><PERSON>em<PERSON>n Judul", "TLS": "", "To access the available model names for downloading,": "<PERSON><PERSON>k mengakses nama model yang tersedia untuk diunduh,", "To access the GGUF models available for downloading,": "Untuk mengakses model GGUF yang tersedia untuk diunduh,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Untuk mengakses WebUI, hubungi administrator. Admin dapat mengelola status pengguna dari Panel Admin.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "Untuk memilih filter di sini, tambahkan filter ke ruang kerja \"<PERSON><PERSON><PERSON>\" terle<PERSON>h dahulu.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Untuk memilih perangkat di sini, tambah<PERSON> ke ruang kerja \"Alat\" terle<PERSON>h dahulu.", "Toast notifications for new updates": "", "Today": "<PERSON> ini", "Toggle settings": "<PERSON><PERSON><PERSON>", "Toggle sidebar": "<PERSON><PERSON><PERSON> bilah sisi", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "Token Untuk Menyimpan Penyegaran Konteks (num_keep)", "Tool created successfully": "Alat berhasil dibuat", "Tool deleted successfully": "<PERSON>at ber<PERSON>", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "Alat berhasil diimpor", "Tool Name": "", "Tool updated successfully": "Alat ber<PERSON>", "Tools": "Alat", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "K atas", "Top P": "<PERSON>", "Transformers": "", "Trouble accessing Ollama?": "Kesulitan mengaks<PERSON>?", "TTS Model": "Model TTS", "TTS Settings": "Pengaturan TTS", "TTS Voice": "Suara TTS", "Type": "Ketik", "Type Hugging Face Resolve (Download) URL": "Ketik Hugging Face Resolve (Unduh) URL", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "<PERSON><PERSON><PERSON> dan <PERSON>", "Update for the latest features and improvements.": "", "Update password": "<PERSON><PERSON><PERSON> kata sandi", "Updated": "", "Updated at": "Diperbar<PERSON> di", "Updated At": "", "Upload": "<PERSON><PERSON><PERSON>", "Upload a GGUF model": "Unggah model GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "Unggah File", "Upload Pipeline": "Unggah Pipeline", "Upload Progress": "<PERSON><PERSON><PERSON><PERSON>", "URL": "", "URL Mode": "Mode URL", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "<PERSON><PERSON><PERSON>", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "pengguna", "User": "", "User location successfully retrieved.": "Lokasi pengguna berhasil diambil.", "Username": "", "Users": "Pengguna", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "Memanfaatkan", "Valid time units:": "Unit waktu yang valid:", "Valves": "<PERSON><PERSON>", "Valves updated": "<PERSON><PERSON>", "Valves updated successfully": "<PERSON><PERSON> be<PERSON><PERSON>", "variable": "variabel", "variable to have them replaced with clipboard content.": "variabel untuk diganti dengan konten papan klip.", "Version": "<PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "<PERSON><PERSON>", "Voice Input": "", "Warning": "Peringatan", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Peringatan: <PERSON><PERSON> atau men<PERSON>bah model penye<PERSON>n, <PERSON><PERSON> ha<PERSON> mengimpor ulang semua dokumen.", "Web": "Web", "Web API": "API Web", "Web Loader Settings": "Pengaturan Pemuat Web", "Web Search": "Pencarian Web", "Web Search Engine": "<PERSON><PERSON>", "Web Search Query Generation": "", "Webhook URL": "URL pengait web", "WebUI Settings": "Pengaturan WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "<PERSON><PERSON> yang <PERSON>u di", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "Bisikan (Lokal)", "Widescreen Mode": "<PERSON> <PERSON><PERSON>", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "<PERSON><PERSON>", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON> saran cepat (misalnya <PERSON> kamu?)", "Write a summary in 50 words that summarizes [topic or keyword].": "<PERSON><PERSON> dalam 50 kata yang merangkum [topik atau kata kunci].", "Write something...": "", "Write your model template content here": "", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "<PERSON><PERSON>", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "<PERSON>a dapat mempersonalisasi interaksi Anda dengan LLM dengan menambahkan kenangan melalui tombol 'Ke<PERSON><PERSON>' di bawah ini, se<PERSON>ga lebih bermanfaat dan disesuaikan untuk Anda.", "You cannot upload an empty file.": "", "You have no archived conversations.": "Anda tidak memiliki percakapan yang diarsipkan.", "You have shared this chat": "Anda telah membagikan obrolan ini", "You're a helpful assistant.": "<PERSON>a adalah asisten yang membantu.", "Your account status is currently pending activation.": "Status akun Anda saat ini sedang menunggu aktivasi.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "Pengaturan Pemuat Youtube"}