{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' nó '-1' gan aon <PERSON>ag.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(m.sh. `sh webui.sh --api --api-auth username_password `)", "(e.g. `sh webui.sh --api`)": "(m.sh. `sh webui.sh --api`)", "(latest)": "(is d<PERSON><PERSON><PERSON>)", "{{ models }}": "{{models}}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Comhráite {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} Ceoldeireadh Riachtanach", "*Prompt node ID(s) are required for image generation": "* Tá ID nód pras ag teastáil chun íomhá a ghiniúint", "A new version (v{{LATEST_VERSION}}) is now available.": "T<PERSON> leagan nua (v {{LATEST_VERSION}}) ar fáil anois.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Úsáidtear samhail tasc agus tascanna á ndéanamh agat mar theidil a ghiniúint do chomhráite agus ceisteanna cuardaigh g<PERSON>in", "a user": "úsáideoir", "About": "Maidir", "Access": "Rochtain", "Access Control": "<PERSON><PERSON><PERSON><PERSON>", "Accessible to all users": "Inrochtana do gach úsáideoir", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "Gníomhachtaithe <PERSON>s", "Actions": "Gníomhartha", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Gníomhachtaigh an t-ordú seo trí \"/{{COMMAND}}\" a chlóscríobh chun ionchur comhrá a dhéanamh.", "Active Users": "Úsáideoirí Gníomhacha", "Add": "<PERSON><PERSON><PERSON>", "Add a model ID": "<PERSON><PERSON><PERSON> <PERSON> samhail leis", "Add a short description about what this model does": "<PERSON>uir cur síos gairid leis faoin méid a dh<PERSON>anann an tsamhail seo", "Add a tag": "<PERSON><PERSON><PERSON> clib leis", "Add Arena Model": "<PERSON><PERSON><PERSON> leis", "Add Connection": "<PERSON><PERSON><PERSON>", "Add Content": "<PERSON><PERSON><PERSON>", "Add content here": "<PERSON><PERSON><PERSON>", "Add custom prompt": "<PERSON><PERSON>r pras sainche<PERSON><PERSON> leis", "Add Files": "<PERSON><PERSON><PERSON>", "Add Group": "<PERSON><PERSON><PERSON>", "Add Memory": "<PERSON><PERSON><PERSON>", "Add Model": "<PERSON><PERSON><PERSON> m<PERSON> leis", "Add Reaction": "", "Add Tag": "<PERSON><PERSON><PERSON>", "Add Tags": "<PERSON><PERSON><PERSON> leis", "Add text content": "<PERSON><PERSON><PERSON> t<PERSON> leis", "Add User": "<PERSON><PERSON><PERSON> leis", "Add User Group": "<PERSON><PERSON><PERSON>áide<PERSON> leis", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Cuirfear na socruithe seo ag coigeartú athruithe go huilíoch ar gach ú<PERSON>á<PERSON>oir.", "admin": "<PERSON><PERSON><PERSON><PERSON>", "Admin": "<PERSON><PERSON><PERSON><PERSON>", "Admin Panel": "<PERSON><PERSON><PERSON>", "Admin Settings": "<PERSON><PERSON><PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Tá rochtain ag riarthóirí ar gach uirlis i g<PERSON>ónaí; teastaíonn uirlisí sannta in aghaidh an tsamhail sa spás oibre ó úsáideoirí.", "Advanced Parameters": "Paraiméadair Cast<PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON><PERSON>", "All Documents": "Gach <PERSON>", "All models deleted successfully": "Scriosadh na samhlacha go léir go rathúil", "Allow Chat Delete": "Ceadaigh Comhrá a Scrio<PERSON>h", "Allow Chat Deletion": "<PERSON><PERSON>", "Allow Chat Edit": "Ceadaigh Eagarthóireacht Comhrá", "Allow File Upload": "Ceadaigh <PERSON>lódáil Comhad", "Allow non-local voices": "<PERSON>g <PERSON> ne<PERSON>-<PERSON><PERSON><PERSON>", "Allow Temporary Chat": "<PERSON><PERSON>", "Allow User Location": "Ceadaigh Su<PERSON>h <PERSON>áideora", "Allow Voice Interruption in Call": "<PERSON><PERSON><PERSON> i nGlao", "Allowed Endpoints": "", "Already have an account?": "Tá cuntas agat cheana féin?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "<PERSON><PERSON><PERSON> eile seachas an top_p, agus tá sé mar aidhm aige cothromaíocht cáilíochta agus éagsúlachta a chinntiú. Léiríonn an paraiméadar p an dóchúlacht íosta go mbreithneofar comhartha, i gcoibhneas le dóchúlacht an chomhartha is dóichí. Mar shampla, le p=0.05 agus dóchúlacht 0.9 ag an comhartha is dóich<PERSON>, déantar logits le luach níos lú ná 0.045 a scagadh amach. (Réamhshocrú: 0.0)", "an assistant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "and": "agus", "and {{COUNT}} more": "agus {{COUNT}} eile", "and create a new shared link.": "agus cruthaigh nasc nua roinnte.", "API Base URL": "URL Bonn API", "API Key": "Eochair API", "API Key created.": "Cruthaíodh Eochair API.", "API Key Endpoint Restrictions": "", "API keys": "Eochracha API", "Application DN": "Feidhmchlár DN", "Application DN Password": "Feidhmchlár DN Pasfhocal", "applies to all users with the \"user\" role": "b<PERSON>ann sé le gach úsáideoir a bhfuil an ról \"úsáideoir\" aige", "April": "Aibreán", "Archive": "Cartlann", "Archive All Chats": "Cartlann Gach Comhrá", "Archived Chats": "Comhráite Cartlann", "archived-chat-export": "gcartlann-comhrá-onnmhairiú", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "An bhfuil tú cinnte gur mhaith leat gach comhrá cartlainne a dhícha<PERSON>?", "Are you sure?": "An bhfuil tú cinnte?", "Arena Models": "<PERSON><PERSON><PERSON><PERSON>", "Artifacts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ask a question": "<PERSON><PERSON><PERSON> c<PERSON>", "Assistant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Attach file": "Ceangail comhad", "Attribute for Username": "<PERSON><PERSON><PERSON> don <PERSON>", "Audio": "<PERSON><PERSON><PERSON>", "August": "Lún<PERSON>", "Authenticate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Auto-Copy Response to Clipboard": "Freagra AutoCopy go Gearrthaisce", "Auto-playback response": "<PERSON><PERSON><PERSON>", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Uathoibríoch1111", "AUTOMATIC1111 Api Auth String": "UATHOMATIC1111 Api Auth Teaghrán", "AUTOMATIC1111 Base URL": "UATHOMATIC1111 BunURL", "AUTOMATIC1111 Base URL is required.": "Tá URL bonn UATHOMATIC1111 ag teastáil.", "Available list": "Liosta atá ar fáil", "available!": "ar fáil!", "Azure AI Speech": "Óráid Azure AI", "Azure Region": "<PERSON><PERSON><PERSON><PERSON> Azure", "Back": "Ar ais", "Bad": "", "Bad Response": "Droch-fhreagra", "Banners": "Meirgí", "Base Model (From)": "<PERSON><PERSON><PERSON> (Ó)", "Batch Size (num_batch)": "<PERSON><PERSON><PERSON> (num_batch)", "before": "roimh", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "Cuardach Bing V7 Críochphointe", "Bing Search V7 Subscription Key": "Eochair S<PERSON>ti<PERSON>is Bing Cuardach V7", "Brave Search API Key": "Eochair API Cuardaigh Brave", "By {{name}}": "Le {{name}}", "Bypass SSL verification for Websites": "Seachbhachtar fíorú SSL do Láithreáin", "Call": "Glaoigh", "Call feature is not supported when using Web STT engine": "<PERSON>í thacaí<PERSON>ar le gné glaonna agus inneall Web STT á úsáid", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Cealaigh", "Capabilities": "<PERSON><PERSON><PERSON>", "Capture": "", "Certificate Path": "<PERSON><PERSON><PERSON>", "Change Password": "<PERSON><PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "", "Chart new frontiers": "Cairt teorainneacha nua", "Chat": "Comhrá", "Chat Background Image": "Íomhá Cúlra Comhrá", "Chat Bubble UI": "Comhrá Bubble UI", "Chat Controls": "<PERSON><PERSON><PERSON><PERSON>", "Chat direction": "<PERSON><PERSON><PERSON>", "Chat Overview": "Forb<PERSON>eathnú ar an", "Chat Permissions": "Ceadanna Comhrá", "Chat Tags Auto-Generation": "Clibeanna Comhrá Auto-Giniúint", "Chats": "Comhráite", "Check Again": "Seiceáil Arís", "Check for updates": "Seiceáil nuashonruithe", "Checking for updates...": "Seiceáil le haghaidh nuashonruithe...", "Choose a model before saving...": "R<PERSON>hn<PERSON><PERSON> samhail sula s<PERSON>n tú...", "Chunk Overlap": "<PERSON><PERSON><PERSON> smu<PERSON>", "Chunk Params": "Chunk Params", "Chunk Size": "<PERSON><PERSON><PERSON>", "Ciphers": "Cipéirí", "Citation": "<PERSON><PERSON>", "Clear memory": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>an", "click here": "cliceáil anseo", "Click here for filter guides.": "Cliceáil anseo le haghaidh treoracha scagaire.", "Click here for help.": "Cliceáil anseo le haghaidh cabhair.", "Click here to": "Cliceáil anseo chun", "Click here to download user import template file.": "Cliceáil anseo chun an comhad iompórtála úsáideora a íoslódáil.", "Click here to learn more about faster-whisper and see the available models.": "Cliceáil anseo chun níos mó a fhoghlaim faoi cogar níos tapúla agus na múnlaí atá ar fáil a fheiceáil.", "Click here to select": "Cliceáil anseo chun rog<PERSON>", "Click here to select a csv file.": "Cliceáil anseo chun comhad csv a roghnú.", "Click here to select a py file.": "Cliceáil anseo chun comhad py a roghnú.", "Click here to upload a workflow.json file.": "Cliceáil anseo chun comhad workflow.json a uaslódáil.", "click here.": "cliceáil anseo.", "Click on the user role button to change a user's role.": "Cliceáil ar an gcnaipe ról úsáideora chun ról úsáideora a athrú.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Diúltaíodh cead scríofa an ghearrtha<PERSON>ce. Seiceáil socruithe do bhrabhsálaí chun an rochtain riachtanach a dheonú.", "Clone": "Clón", "Close": "Dún", "Code execution": "<PERSON><PERSON>d a <PERSON>gh<PERSON>ío<PERSON>ú", "Code formatted successfully": "<PERSON><PERSON><PERSON> go rath<PERSON>il", "Collection": "Bailiúchán", "Color": "<PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL Bonn ComfyUI", "ComfyUI Base URL is required.": "Teastaíonn URL ComfyUI Base.", "ComfyUI Workflow": "Sreabhadh Oibre ComfyUI", "ComfyUI Workflow Nodes": "<PERSON><PERSON><PERSON> Oibre ComfyUI", "Command": "Ordú", "Completions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Concurrent Requests": "Iarrataí Comhthéime", "Configure": "<PERSON><PERSON><PERSON><PERSON>", "Configure Models": "", "Confirm": "<PERSON><PERSON><PERSON><PERSON>", "Confirm Password": "<PERSON><PERSON><PERSON><PERSON>", "Confirm your action": "Deimhnigh do ghníomh", "Confirm your new password": "", "Connections": "Naisc", "Contact Admin for WebUI Access": "<PERSON><PERSON><PERSON>g<PERSON>áil le Riarachán le haghaidh Rochtana WebUI", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction": "<PERSON><PERSON><PERSON><PERSON>", "Context Length": "Fad Comhthéacs", "Continue Response": "<PERSON><PERSON><PERSON><PERSON> ar aghaidh", "Continue with {{provider}}": "Lean ar aghaidh le {{provider}}", "Continue with Email": "<PERSON>n ar a<PERSON><PERSON>", "Continue with LDAP": "Lean ar a<PERSON><PERSON> le LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "R<PERSON>ú conas a roinntear téacs teachtaireachta d'iarratais TTS. Roinneann 'poncaíocht' ina a<PERSON>, s<PERSON><PERSON><PERSON> 'míreanna' i mí<PERSON>nna, agus <PERSON>nn 'aon' an teachtaireacht mar shreang amh<PERSON>in.", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "Rialaíonn sé an chothromaíocht idir comhleanúnachas agus é<PERSON> an aschuir. <PERSON>idh téacs níos dírithe agus níos soiléire mar thoradh ar luach níos ísle. (Réamhshocrú: 5.0)", "Copied": "<PERSON><PERSON><PERSON><PERSON>", "Copied shared chat URL to clipboard!": "Cóipeáladh URL an chomhrá roinnte chuig an ngearrthaisce!", "Copied to clipboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> go gear", "Copy": "Cóipeáil", "Copy last code block": "Cóipeáil bloc cód <PERSON>", "Copy last response": "<PERSON><PERSON><PERSON><PERSON><PERSON> an fhreagairt", "Copy Link": "Cóipeáil Nasc", "Copy to clipboard": "Cóipeáil chuig an ngearrthaisce", "Copying to clipboard was successful!": "D'éirigh le cóip<PERSON>áil chuig an ngearrthaisce!", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Create a knowledge base": "<PERSON><PERSON><PERSON><PERSON> bonn eolais", "Create a model": "<PERSON><PERSON><PERSON><PERSON>l", "Create Account": "<PERSON><PERSON><PERSON><PERSON>", "Create Admin Account": "<PERSON><PERSON><PERSON><PERSON>", "Create Channel": "", "Create Group": "<PERSON><PERSON><PERSON><PERSON>", "Create Knowledge": "<PERSON><PERSON><PERSON><PERSON>", "Create new key": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>air nua", "Create new secret key": "<PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON> rún<PERSON> nua", "Created at": "Cruthaithe ag", "Created At": "Cruthaithe Ag", "Created by": "Cruthaithe ag", "CSV Import": "Iompórtáil CSV", "Current Model": "<PERSON><PERSON><PERSON>", "Current Password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom": "Sainch<PERSON>pt<PERSON>", "Dark": "<PERSON><PERSON><PERSON>", "Database": "<PERSON><PERSON><PERSON><PERSON>", "December": "<PERSON><PERSON><PERSON>", "Default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Default (Open AI)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Oscail AI)", "Default (SentenceTransformers)": "Réamhshocraithe (SentenceTransFormers)", "Default Model": "<PERSON><PERSON><PERSON>", "Default model updated": "An tsamhail r<PERSON>e", "Default Models": "", "Default permissions": "<PERSON><PERSON><PERSON> r<PERSON>", "Default permissions updated successfully": "D'<PERSON><PERSON><PERSON> le ceadanna réam<PERSON>e a nuashonrú", "Default Prompt Suggestions": "Moltaí Pras Réamhshocraithe", "Default to 389 or 636 if TLS is enabled": "Réamhshocrú go 389 nó 636 má tá TLS cumasaithe", "Default to ALL": "Réamhshocrú do GACH", "Default User Role": "Ról Úsáideora Réamhshoc", "Delete": "<PERSON><PERSON><PERSON>", "Delete a model": "<PERSON><PERSON><PERSON> sa<PERSON>", "Delete All Chats": "<PERSON><PERSON>s Gach Comhrá", "Delete All Models": "<PERSON><PERSON><PERSON>", "Delete chat": "Scrios comhrá", "Delete Chat": "<PERSON><PERSON>s <PERSON>hr<PERSON>", "Delete chat?": "Scrios comhrá?", "Delete folder?": "<PERSON><PERSON><PERSON>?", "Delete function?": "<PERSON><PERSON><PERSON> feidhm?", "Delete Message": "", "Delete prompt?": "<PERSON><PERSON>s pras?", "delete this link": "scrios an nasc seo", "Delete tool?": "Uirlis a scriosadh?", "Delete User": "Scrios <PERSON>", "Deleted {{deleteModelTag}}": "Scriosta {{deleteModelTag}}", "Deleted {{name}}": "Scrio<PERSON> {{name}}", "Deleted User": "Úsáideoir Scriosta", "Describe your knowledge base and objectives": "<PERSON><PERSON><PERSON> cur síos ar do bhunachar eolais agus do chuspóirí", "Description": "<PERSON><PERSON> síos", "Disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover a function": "<PERSON><PERSON>gh amach feidhm", "Discover a model": "<PERSON><PERSON><PERSON> amach samhail", "Discover a prompt": "<PERSON><PERSON>gh amach pras", "Discover a tool": "<PERSON><PERSON><PERSON> amach uirlis", "Discover wonders": "<PERSON><PERSON><PERSON> amach <PERSON>", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON>, íoslódáil agus iniúchadh feidhmeanna saincheaptha", "Discover, download, and explore custom prompts": "Leideanna saincheaptha a fháil amach, a íoslódáil agus a iniúchadh", "Discover, download, and explore custom tools": "Uirlisí sain<PERSON> a fháil amach, íoslódáil agus iniúchadh", "Discover, download, and explore model presets": "Réamhs<PERSON><PERSON><PERSON><PERSON> samhail a fháil amach, a íoslódáil agus a iniúchadh", "Dismissible": "Dífhostaithe", "Display": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Display Emoji in Call": "Taispeáin Emoji i nGlao", "Display the username instead of You in the Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON> an t-ainm ú<PERSON>áideora in ionad Tú sa Comhrá", "Displays citations in the response": "Taispeánann sé luanna sa fhreagra", "Dive into knowledge": "<PERSON><PERSON><PERSON> is<PERSON> e<PERSON>s", "Do not install functions from sources you do not fully trust.": "Ná suiteáil feidhmeanna ó fhoinsí nach bhfuil muinín i<PERSON>án agat.", "Do not install tools from sources you do not fully trust.": "Ná suiteáil uirlisí ó fho<PERSON>í nach bhfuil muinín i<PERSON>lán agat.", "Document": "Doiciméad", "Documentation": "Doiciméadú", "Documents": "<PERSON><PERSON><PERSON><PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "ní dh<PERSON>anann sé aon naisc sheach<PERSON>, agus fanann do chuid sonraí go slán ar do fhreastalaí a óstáiltear go háitiúil.", "Don't have an account?": "<PERSON><PERSON>l cuntas agat?", "don't install random functions from sources you don't trust.": "ná suiteáil feidhmeanna randamacha ó fhoinsí nach bhfuil muinín agat.", "don't install random tools from sources you don't trust.": "ná suiteáil uirlisí randamacha ó fhoinsí nach bhfuil muinín agat.", "Done": "Déanta", "Download": "Í<PERSON>lódáil", "Download canceled": "Íoslódáil cealaithe", "Download Database": "Íoslódáil Bunachair", "Drag and drop a file to upload or select a file to view": "Tarraing agus scaoil comhad le huaslódáil nó roghnaigh comhad le féachaint air", "Draw": "<PERSON><PERSON><PERSON>", "Drop any files here to add to the conversation": "<PERSON>aoil aon chomhaid anseo le cur leis an gcomhrá", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "m.sh. '30s', '10m'. Is iad aonaid ama bailí ná 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "m.<PERSON><PERSON> chun profanity a bhaint as té<PERSON>s", "e.g. My Filter": "m.sh. <PERSON>", "e.g. My Tools": "e.g. <PERSON>", "e.g. my_filter": "m.sh. mo_scagaire", "e.g. my_tools": "m.sh. mo_uir<PERSON><PERSON>", "e.g. Tools for performing various operations": "m.sh. <PERSON><PERSON><PERSON><PERSON> chun oibríochtaí éag<PERSON>úla a dhéanamh", "Edit": "C<PERSON>r in eagar", "Edit Arena Model": "<PERSON><PERSON><PERSON> in Eagar", "Edit Channel": "", "Edit Connection": "<PERSON><PERSON>r <PERSON> in Eagar", "Edit Default Permissions": "<PERSON><PERSON><PERSON> Réamhs<PERSON>raithe in Eagar", "Edit Memory": "<PERSON><PERSON><PERSON> in eagar", "Edit User": "<PERSON><PERSON><PERSON> in eagar", "Edit User Group": "Cuir Grúpa Úsáideoirí in Eagar", "ElevenLabs": "Eleven Labs", "Email": "Ríomhphost", "Embark on adventures": "Dul ar <PERSON>", "Embedding Batch Size": "<PERSON><PERSON><PERSON> a ionchorprú", "Embedding Model": "<PERSON><PERSON><PERSON>", "Embedding Model Engine": "<PERSON><PERSON><PERSON>", "Embedding model set to \"{{embedding_model}}\"": "<PERSON><PERSON><PERSON> atá socraithe go \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Cumasaigh Comhroinnt Pobail", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Cumasaigh Glasáil Cuimhne (mlock) chun sonraí samhaltaithe a chosc ó RAM. Glasálann an rogha seo sraith oibre leathanaigh an mhúnla isteach i RAM, ag cinntiú nach ndéanfar iad a mhalartú go diosca. Is féidir leis seo cabhrú le feidhmíocht a choinneáil trí lochtanna leathanaigh a sheachaint agus rochtain tapa ar shonraí a chinntiú.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Cumasaigh Mapáil Cuimhne (mmap) chun sonraí samhla a lódáil. Ligeann an rogha seo don chóras stóráil diosca a úsáid mar leathnú ar RAM trí chomhaid diosca a chóireáil amhail is dá mba i RAM iad. Is féidir leis seo feidhmíocht na samhla a fheabhsú trí rochtain níos tapúla ar shonraí a cheadú. Mar sin féin, d'fhéadfadh sé nach n-oibreoidh sé i gceart le gach córas agus féadfaidh sé méid suntasach spáis diosca a ithe.", "Enable Message Rating": "Cumasaigh Rátáil Teachtai", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "Cumasaigh sampláil Mirostat chun seachrán a rialú. (Réamhshocrú: 0, 0 = D<PERSON><PERSON>masaithe, 1 = Mirostat, 2 = Mirostat 2.0)", "Enable New Sign Ups": "Cumasaigh Clár<PERSON>", "Enable Web Search": "<PERSON>uma<PERSON><PERSON>", "Enabled": "<PERSON><PERSON><PERSON><PERSON>", "Engine": "<PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON> cinn<PERSON> go bhfuil 4 cholún san ord seo i do chomhad CSV: Ainm, Ríomhphost, Pasfhocal, Ról.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON> is<PERSON>ach teach<PERSON>t {{role}} anseo", "Enter a detail about yourself for your LLMs to recall": "<PERSON><PERSON><PERSON> is<PERSON>ach mi<PERSON>raí fút féin chun do LLManna a mheabhrú", "Enter api auth string (e.g. username:password)": "<PERSON><PERSON><PERSON> is<PERSON>ach sreang auth api (m.sh. ainm úsáideora: pasfhocal)", "Enter Application DN": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> D<PERSON>", "Enter Application DN Password": "Iontráil Feidhmchlár DN Pasfhocal", "Enter Bing Search V7 Endpoint": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> V7 Críochphointe", "Enter Bing Search V7 Subscription Key": "<PERSON><PERSON><PERSON> is<PERSON><PERSON>air <PERSON>ti<PERSON> Bing Cuardach V7", "Enter Brave Search API Key": "<PERSON><PERSON><PERSON> is<PERSON>ach Eochair API Brave Cuardach", "Enter certificate path": "<PERSON><PERSON><PERSON> is<PERSON>ach cos<PERSON> an teastais", "Enter CFG Scale (e.g. 7.0)": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> (m.sh. 7.0)", "Enter Chunk Overlap": "<PERSON><PERSON><PERSON> is<PERSON><PERSON>luí", "Enter Chunk Size": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> an <PERSON>", "Enter description": "<PERSON><PERSON><PERSON>", "Enter Github Raw URL": "<PERSON><PERSON><PERSON> is<PERSON>ach URL Github Raw", "Enter Google PSE API Key": "<PERSON><PERSON>r isteach Eochair API Google PSE", "Enter Google PSE Engine Id": "<PERSON><PERSON>r isteach ID Inneall Google PSE", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON> (m.sh. 512x512)", "Enter Jina API Key": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> Eochair API Jina", "Enter Kagi Search API Key": "", "Enter language codes": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> c<PERSON> teanga", "Enter Model ID": "Iontráil ID <PERSON>", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> chlib samhail (m.sh. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Iontráil Líon na gCéimeanna (m.sh. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> (m.sh. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> (m.sh. <PERSON>)", "Enter Score": "Iontráil <PERSON>ór", "Enter SearchApi API Key": "<PERSON><PERSON>r isteach Eochair API SearchAPI", "Enter SearchApi Engine": "<PERSON><PERSON><PERSON> is<PERSON>ach <PERSON>l SearchAPI", "Enter Searxng Query URL": "<PERSON><PERSON><PERSON> is<PERSON>ach URL Ceist Searxng", "Enter Seed": "<PERSON><PERSON><PERSON> is<PERSON><PERSON>", "Enter Serper API Key": "C<PERSON>r isteach Eochair API Serper", "Enter Serply API Key": "Cuir isteach Eochair API Serply", "Enter Serpstack API Key": "<PERSON><PERSON>r isteach Eochair API Serpstack", "Enter server host": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> ó<PERSON> frea<PERSON>", "Enter server label": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> lip<PERSON>ad frea<PERSON>", "Enter server port": "C<PERSON>r isteach port freastalaí", "Enter stop sequence": "<PERSON><PERSON><PERSON> is<PERSON>ach se<PERSON>h stad", "Enter system prompt": "<PERSON><PERSON><PERSON> is<PERSON>ach an chóras pras", "Enter Tavily API Key": "<PERSON><PERSON>r isteach eochair API Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "<PERSON><PERSON><PERSON> is<PERSON>ach URL freastalaí Tika", "Enter Top K": "<PERSON><PERSON><PERSON> is<PERSON><PERSON>", "Enter URL (e.g. http://127.0.0.1:7860/)": "Iontráil URL (m.sh. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Iontráil URL (m.sh. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> do <PERSON>mhphost", "Enter Your Full Name": "<PERSON><PERSON><PERSON> is<PERSON><PERSON>", "Enter your message": "<PERSON><PERSON><PERSON> is<PERSON>ach do theachtaireacht", "Enter your new password": "", "Enter Your Password": "<PERSON><PERSON><PERSON> isteach do phasfhocal", "Enter your prompt": "", "Enter Your Role": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> do Ról", "Enter Your Username": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>á<PERSON>", "Enter your webhook URL": "", "Error": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ERROR": "EARRÁID", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Meastóireachtaí", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Sampla: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Sampla: GACH", "Example: ou=users,dc=foo,dc=example": "Sampla: ou=úsáideoirí,dc=foo,dc=sampla", "Example: sAMAccountName or uid or userPrincipalName": "Sampla: sAMAaccountName nó uid nó userPrincipalName", "Exclude": "Eisigh", "Experimental": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Explore the cosmos": "<PERSON><PERSON><PERSON> ar an cosmos", "Export": "Easpórtáil", "Export All Archived Chats": "Easpórtáil Gach Comhrá <PERSON>", "Export All Chats (All Users)": "Easpórtáil gach comhrá (Gach Úsáideoir)", "Export chat (.json)": "Easpórtáil comhrá (.json)", "Export Chats": "Comhráite Easpórtá", "Export Config to JSON File": "Easpórtáil Cumraíocht chuig Comhad JSON", "Export Functions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Export Models": "Múnlaí a Easpórtáil", "Export Presets": "Easpórtáil Gach Comhrá <PERSON>", "Export Prompts": "<PERSON><PERSON><PERSON>", "Export to CSV": "Easpórtáil go CSV", "Export Tools": "<PERSON><PERSON><PERSON><PERSON>", "External Models": "<PERSON><PERSON><PERSON><PERSON>", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "<PERSON><PERSON> ar an gcomhad a chur leis.", "Failed to create API Key.": "<PERSON><PERSON> ar an eochair API a chruthú.", "Failed to read clipboard contents": "<PERSON><PERSON> ar <PERSON> a lé", "Failed to save models configuration": "", "Failed to update settings": "<PERSON><PERSON> ar shocruithe a nuashonrú", "February": "<PERSON><PERSON><PERSON>", "Feedback History": "<PERSON><PERSON>", "Feedbacks": "Aiseola<PERSON>", "File": "Comhad", "File added successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON> leis an g<PERSON>had a chur leis.", "File content updated successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON> le h<PERSON>r an chomhaid a nuashonrú.", "File Mode": "<PERSON><PERSON><PERSON>", "File not found.": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> an comhad.", "File removed successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON> le baint an chomhaid.", "File size should not exceed {{maxSize}} MB.": "<PERSON><PERSON><PERSON> chóir go mbeadh méid an chomhaid níos mó ná {{maxSize}} MB.", "File uploaded successfully": "", "Files": "<PERSON><PERSON><PERSON><PERSON>", "Filter is now globally disabled": "Tá an scagaire faoi mhíchumas go domhanda", "Filter is now globally enabled": "Tá an scagaire cumasaithe go domhanda anois", "Filters": "Scagairí", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Braithíodh spoofing méarloirg: <PERSON>í féidir teach<PERSON> a ú<PERSON>áid mar avatar. Réamhshocrú ar íomhá pr<PERSON><PERSON><PERSON><PERSON>.", "Fluidly stream large external response chunks": "Sruthaigh codanna móra freagartha seachtracha go sreabhach", "Focus chat input": "<PERSON><PERSON><PERSON> comhr<PERSON> fócas", "Folder deleted successfully": "<PERSON><PERSON><PERSON><PERSON> an fill<PERSON> go rathúil", "Folder name cannot be empty": "<PERSON><PERSON> féidir ainm fillte<PERSON>in a bheith folamh", "Folder name cannot be empty.": "<PERSON><PERSON> féidir ainm fillte<PERSON>in a bheith folamh.", "Folder name updated successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> le hainm an fhill<PERSON>in a nuashonrú", "Forge new paths": "<PERSON><PERSON><PERSON> co<PERSON> nua a chruthú", "Form": "<PERSON><PERSON><PERSON>", "Format your variables using brackets like this:": "Formáidigh na hathróga ag baint úsáide as l<PERSON><PERSON><PERSON><PERSON> mar seo:", "Frequency Penalty": "<PERSON><PERSON><PERSON>", "Function": "<PERSON><PERSON><PERSON>", "Function created successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> feidhm go rath<PERSON>il", "Function deleted successfully": "<PERSON><PERSON><PERSON> scriosta go rath<PERSON>il", "Function Description": "<PERSON><PERSON> s<PERSON><PERSON> a<PERSON>", "Function ID": "<PERSON>", "Function is now globally disabled": "Tá an fheidhm faoi mhíchumas go domhanda", "Function is now globally enabled": "Tá feidhm cumasaithe go domhanda anois", "Function Name": "<PERSON><PERSON>", "Function updated successfully": "<PERSON><PERSON><PERSON> n<PERSON>e", "Functions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution": "Ligeann feidhmeanna forghníomhú cód", "Functions allow arbitrary code execution.": "<PERSON><PERSON><PERSON>nn feidhmeanna forghníomhú cód treallach.", "Functions imported successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "General": "Ginearálta", "General Settings": "<PERSON><PERSON><PERSON><PERSON>", "Generate Image": "<PERSON><PERSON><PERSON>", "Generating search query": "<PERSON><PERSON><PERSON><PERSON> ceist cuardaigh", "Get started": "<PERSON><PERSON><PERSON> t<PERSON> leis", "Get started with {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON> t<PERSON> {{WEBUI_NAME}}", "Global": "<PERSON><PERSON><PERSON>", "Good Response": "<PERSON><PERSON><PERSON>", "Google Drive": "", "Google PSE API Key": "Eochair API Google PSE", "Google PSE Engine Id": "ID Inneall Google PSE", "Group created successfully": "<PERSON><PERSON><PERSON><PERSON> cruthaithe go rathúil", "Group deleted successfully": "<PERSON>'<PERSON><PERSON><PERSON> le scrio<PERSON>h an g<PERSON>pa", "Group Description": "Cur síos ar an nGrúpa", "Group Name": "<PERSON><PERSON>", "Group updated successfully": "D'éiri<PERSON> le nua<PERSON> an ghr<PERSON>pa", "Groups": "Grúpaí", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h: mm a", "Haptic Feedback": "<PERSON><PERSON><PERSON><PERSON>", "Harmful or offensive": "", "has no conversations.": "níl aon chomhr<PERSON>ite aige.", "Hello, {{name}}": "Dia duit, {{name}}", "Help": "<PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "<PERSON><PERSON><PERSON>igh linn an clár ceannai<PERSON><PERSON> pobail is fearr a chruthú trí do stair aiseolais a roinnt!", "Hex Color": "Dath Heics", "Hex Color - Leave empty for default color": "Dath He<PERSON> - <PERSON><PERSON><PERSON> folamh don dath r<PERSON><PERSON><PERSON><PERSON><PERSON>e", "Hide": "<PERSON><PERSON><PERSON>", "Host": "<PERSON><PERSON><PERSON>", "How can I help you today?": "Conas is féidir liom cabhrú leat inniu?", "How would you rate this response?": "Cad é mar a mheasfá an freagra seo?", "Hybrid Search": "<PERSON><PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Admhaím gur léigh mé agus tuigim impleachtaí mo ghníomhaíochta. Táim ar an eolas faoi na rios<PERSON>í a bhaineann le cód treallach a fhorghníomhú agus tá iontaofacht na foinse fíoraithe agam.", "ID": "ID", "Ignite curiosity": "Las fiosracht", "Image Compression": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON> (Turgnaimh)", "Image Generation Engine": "<PERSON><PERSON><PERSON> G<PERSON>", "Image Max Compression Size": "", "Image Settings": "<PERSON><PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Chats": "Comhráite iompórtá", "Import Config from JSON File": "Cumraíocht Iompórtáil ó Chomhad JSON", "Import Functions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Models": "Múnlaí a Iompórtáil", "Import Presets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Prompts": "<PERSON><PERSON><PERSON>", "Import Tools": "<PERSON><PERSON><PERSON><PERSON>", "Include": "<PERSON><PERSON><PERSON> san <PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON>r bratach `--api-auth` san á<PERSON><PERSON>h agus webui stable-diffusion-reatha á rith", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON>r bratach `--api` san á<PERSON><PERSON>h agus webui cobhsaí-scaipthe á rith", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "B<PERSON>nn tionchar aige ar chomh tapa agus a fhreagraíonn an t-algartam d’aiseolas ón téacs ginte. <PERSON><PERSON>h coigeartuithe níos moille mar thoradh ar ráta foghlama níos <PERSON>, agus déan<PERSON>idh ráta foghlama níos airde an t-algartam níos freagraí. (Réamhshocrú: 0.1)", "Info": "<PERSON><PERSON><PERSON>", "Input commands": "Orduithe ionchuir", "Install from Github URL": "Suiteáil ó Github URL", "Instant Auto-Send After Voice Transcription": "Seoladh Uathoibríoch L<PERSON>ithreach Tar éis", "Interface": "Comhéadan", "Invalid file format.": "<PERSON><PERSON><PERSON> comhaid <PERSON>.", "Invalid Tag": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "is typing...": "", "January": "<PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "Jina API Eochair", "join our Discord for help.": "b<PERSON> in<PERSON>r <PERSON>rd chun cabhair a fháil.", "JSON": "JSON", "JSON Preview": "Réamhamharc JSON", "July": "<PERSON><PERSON><PERSON>", "June": "<PERSON><PERSON><PERSON><PERSON>", "JWT Expiration": "Éag JWT", "JWT Token": "Comhartha JWT", "Kagi Search API Key": "", "Keep Alive": "<PERSON><PERSON><PERSON>", "Key": "Eochair", "Keyboard shortcuts": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "Knowledge": "<PERSON><PERSON><PERSON>", "Knowledge Access": "Roch<PERSON>", "Knowledge created successfully.": "<PERSON><PERSON><PERSON> crutha<PERSON>e go rath<PERSON>il.", "Knowledge deleted successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON> leis an eolas a scriosadh.", "Knowledge reset successfully.": "D'éirigh le hathshocrú eolais.", "Knowledge updated successfully": "<PERSON>'<PERSON><PERSON><PERSON> leis an eolas a nuashonrú", "Label": "Lipéad", "Landing Page Mode": "<PERSON><PERSON><PERSON>", "Language": "Teanga", "Last Active": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Last Modified": "<PERSON><PERSON><PERSON><PERSON>", "Last reply": "", "Latest users": "", "LDAP": "LDAP", "LDAP server updated": "Nuashonraíodh freastalaí LDAP", "Leaderboard": "<PERSON>", "Leave empty for unlimited": "<PERSON><PERSON><PERSON> folamh le haghaidh ne<PERSON>", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "<PERSON><PERSON><PERSON> folamh chun gach múnla ó chríochphointe \"{{URL}}/api/tags\" a chur san á<PERSON>h", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "<PERSON><PERSON><PERSON> folamh chun gach múnla ón gcríochphointe \"{{URL}}/models\" a chur san á<PERSON>h", "Leave empty to include all models or select specific models": "<PERSON><PERSON><PERSON> folamh chun gach múnla a chur san <PERSON>h nó roghnaigh múnlaí sonracha", "Leave empty to use the default prompt, or enter a custom prompt": "<PERSON><PERSON><PERSON> folamh chun an pras réamhs<PERSON>e a ú<PERSON>, nó cuir isteach pras saincheaptha", "Light": "Solas", "Listening...": "Éisteacht...", "Local": "Áitiúil", "Local Models": "<PERSON><PERSON><PERSON><PERSON>", "Lost": "C<PERSON><PERSON>dh", "LTR": "LTR", "Made by OpenWebUI Community": "Déanta ag OpenWebUI Community", "Make sure to enclose them with": "<PERSON><PERSON><PERSON> cinnte iad a cheangal le", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON>an cinnte comhad workflow.json a onnmhairiú mar fhormáid API ó ComfyUI.", "Manage": "Bainistiú", "Manage Arena Models": "<PERSON><PERSON><PERSON>", "Manage Ollama": "<PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "Bainistigh Naisc API Ollama", "Manage OpenAI API Connections": "Bainistigh Naisc API OpenAI", "Manage Pipelines": "<PERSON><PERSON><PERSON>", "March": "Márta", "Max Tokens (num_predict)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (num_predicate)", "Max Upload Count": "<PERSON><PERSON><PERSON>", "Max Upload Size": "<PERSON><PERSON><PERSON>", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Is féidir uasmhéid de 3 mhúnla a íoslódáil ag an am Bain triail as ar<PERSON> n<PERSON>.", "May": "<PERSON><PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Taisp<PERSON><PERSON><PERSON> cu<PERSON> atá inrochtana ag LLManna anseo.", "Memory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Memory added successfully": "<PERSON><PERSON><PERSON><PERSON> cuimhne leis go", "Memory cleared successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> glanta go rath<PERSON>il", "Memory deleted successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> scriosta go rathúil", "Memory updated successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>e", "Merge Responses": "Cumaisc Freagraí", "Message rating should be enabled to use this feature": "Ba cheart rátáil teachtaireachta a chumasú chun an ghné seo a <PERSON>áid", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON>í roinnfear teachtaireachtaí a sheolann tú tar éis do nasc a chruthú. Beidh úsáideoirí leis an URL in ann féachaint ar an gcomhrá roinnte.", "Min P": "<PERSON>", "Minimum Score": "<PERSON><PERSON><PERSON>", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM LL, BBBB", "MMMM DD, YYYY HH:mm": "MMMM LL, BBBB UU:nn", "MMMM DD, YYYY hh:mm:ss A": "MMMM LL, BBBB uu:nn:ss A", "Model": "<PERSON><PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "<PERSON><PERSON><PERSON>h an tsamhail '{{modelName}}' a íoslód<PERSON> go rathúil.", "Model '{{modelTag}}' is already in queue for downloading.": "<PERSON><PERSON> m<PERSON>la ‘{{modelTag}}’ sa scuaine cheana féin le híoslódáil.", "Model {{modelId}} not found": "<PERSON><PERSON><PERSON> {{modelId}} gan <PERSON><PERSON><PERSON>", "Model {{modelName}} is not vision capable": "<PERSON><PERSON><PERSON> sa<PERSON> {{modelName}} in ann amharc", "Model {{name}} is now {{status}}": "Tá samhail {{name}} {{status}} anois", "Model accepts image inputs": "Glacann m<PERSON>la le hi<PERSON>chuir", "Model created successfully!": "<PERSON><PERSON><PERSON><PERSON><PERSON> múnla go rath<PERSON>il!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Braitheadh ​​conair chóras comhad samhail. Teastaíonn mionainm múnla le haghaidh nuashonraithe, ní féidir leanúint ar aghaidh.", "Model Filtering": "Scagadh Múnla", "Model ID": "ID Múnla", "Model IDs": "<PERSON><PERSON>", "Model Name": "<PERSON><PERSON>", "Model not selected": "<PERSON><PERSON><PERSON> nach r<PERSON>", "Model Params": "<PERSON><PERSON><PERSON>", "Model Permissions": "<PERSON><PERSON><PERSON>", "Model updated successfully": "An tsamhail nuashon<PERSON>e", "Modelfile Content": "<PERSON><PERSON><PERSON>", "Models": "Múnlaí", "Models Access": "Rochtain Múnlaí", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "níos mó", "More": "<PERSON><PERSON><PERSON><PERSON>", "Name": "Ainm", "Name your knowledge base": "<PERSON><PERSON>r ainm ar do bhunachar eolais", "New Chat": "<PERSON><PERSON><PERSON><PERSON>", "New folder": "", "New Password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "new-channel": "", "No content found": "<PERSON><PERSON><PERSON> a<PERSON>", "No content to speak": "<PERSON><PERSON><PERSON> a<PERSON> le <PERSON>", "No distance available": "Níl achar ar fáil", "No feedbacks found": "<PERSON><PERSON><PERSON> aon a<PERSON>", "No file selected": "<PERSON><PERSON><PERSON> aon chom<PERSON> r<PERSON>he", "No files found.": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> aon chomhaid.", "No groups with access, add a group to grant access": "Gan aon ghrúpa a bhfuil rochtain acu, cuir grúpa leis chun rochtain a dheonú", "No HTML, CSS, or JavaScript content found.": "Níor aimsíodh aon <PERSON> HTML, CSS nó JavaScript.", "No knowledge found": "<PERSON><PERSON><PERSON> aon eolas", "No model IDs": "<PERSON><PERSON> <PERSON><PERSON> m<PERSON>", "No models found": "<PERSON><PERSON><PERSON> aon mh<PERSON>", "No models selected": "", "No results found": "Níl aon torthaí le fáil", "No search query generated": "<PERSON><PERSON> ghin<PERSON>ar aon cheist cuardaigh", "No source available": "Níl aon fhoinse ar fáil", "No users were found.": "<PERSON><PERSON><PERSON> <PERSON> aon ú<PERSON>áideoirí.", "No valves to update": "Gan comhl<PERSON>í le nuashonrú", "None": "<PERSON><PERSON>", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nóta: <PERSON><PERSON> sho<PERSON>íonn tú <PERSON>, ní thab<PERSON><PERSON>h an cuardach ach doiciméid a bhfuil scór níos mó ná nó cothrom leis an scór íosta ar ais.", "Notes": "Nótaí", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Fógraí", "November": "<PERSON><PERSON><PERSON>", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "Aitheantas OAuth", "October": "<PERSON><PERSON><PERSON><PERSON>", "Off": "<PERSON><PERSON><PERSON>", "Okay, Let's Go!": "<PERSON><PERSON> go leor, <PERSON><PERSON><PERSON><PERSON><PERSON>!", "OLED Dark": "OLED Dorcha", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API faoi mhíchumas", "Ollama API settings updated": "Nuashonraíodh socruithe Olama API", "Ollama Version": "<PERSON><PERSON>", "On": "Ar", "Only alphanumeric characters and hyphens are allowed": "<PERSON>í chead<PERSON>í<PERSON>ar ach carachtair alfa-uimhriúla agus fleis<PERSON>", "Only alphanumeric characters and hyphens are allowed in the command string.": "Ní chead<PERSON>ar ach carachtair alfauméireacha agus braithíní sa sreangán ordaithe.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON>í féidir ach bail<PERSON>ú<PERSON>in a chur in eagar, bonn eolais nua a chruthú chun doiciméid a chur in eagar/a chur leis.", "Only select users and groups with permission can access": "Ní féidir ach le húsáideoirí roghnaithe agus le grúpaí a bhfuil cead acu rochtain a fháil", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ups! Is cosúil go bhfuil an URL neamhbhailí. Seiceáil faoi dhó le do thoil agus iarracht arís.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Úps! Tá comhaid fós á n-uaslódáil. Fan go mbeidh an uaslódáil críochnaithe.", "Oops! There was an error in the previous response.": "Úps! Bhí earráid sa fhreagra roimhe seo.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ups! Tá modh gan tacaíocht á úsáid agat (tosaigh am<PERSON>). Freastal ar an WebUI ón gcúltaca le do thoil.", "Open in full screen": "Oscail i scáileán iomlán", "Open new chat": "Oscail comhrá nua", "Open WebUI uses faster-whisper internally.": "Úsáideann Open WebUI cogar níos tapúla go hinmheánach.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Úsáideann Open WebUI úsáidí SpeechT5 agus CMU leabaithe cainteoir Artach.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Tá leagan WebUI oscailte (v{{OPEN_WEBUI_VERSION}}) níos ísle ná an leagan riachtanach (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Cumraíocht API OpenAI", "OpenAI API Key is required.": "Tá Eochair API OpenAI ag teastáil.", "OpenAI API settings updated": "Nuashonraíodh socruithe OpenAI API", "OpenAI URL/Key required.": "Teastaíonn URL/eochair OpenAI.", "or": "nó", "Organize your users": "Eagraigh do chuid úsáideoirí", "OUTPUT": "ASCHUR", "Output format": "<PERSON><PERSON><PERSON>", "Overview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON><PERSON><PERSON>", "Password": "Pasfhocal", "Paste Large Text as File": "<PERSON><PERSON><PERSON><PERSON>", "PDF document (.pdf)": "Doiciméad PDF (.pdf)", "PDF Extract Images (OCR)": "Íomhánna Sliocht PDF (OCR)", "pending": "ar feithea<PERSON>h", "Permission denied when accessing media devices": "<PERSON><PERSON> di<PERSON>the nuair a bhíonn rochtain agat", "Permission denied when accessing microphone": "<PERSON><PERSON> di<PERSON>the agus tú ag rochtain ar", "Permission denied when accessing microphone: {{error}}": "<PERSON><PERSON> di<PERSON>the agus tú ag teacht ar mhicreafón: {{error}}", "Permissions": "<PERSON><PERSON><PERSON>", "Personalization": "Pearsantú", "Pin": "<PERSON><PERSON><PERSON><PERSON>", "Pinned": "Pinneáilte", "Pioneer insights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pipeline deleted successfully": "Scriosta píblíne go rathúil", "Pipeline downloaded successfully": "Íoslódáilte píblíne", "Pipelines": "Píblínte", "Pipelines Not Detected": "<PERSON><PERSON><PERSON>", "Pipelines Valves": "Comhlaí Píblíne", "Plain text (.txt)": "<PERSON><PERSON>acs simplí (.txt)", "Playground": "<PERSON><PERSON><PERSON>", "Please carefully review the following warnings:": "<PERSON><PERSON><PERSON> cú<PERSON>ch ar na rabhaidh seo a leanas le do thoil:", "Please enter a prompt": "<PERSON><PERSON><PERSON> is<PERSON><PERSON> leid", "Please fill in all fields.": "<PERSON><PERSON><PERSON> isteach gach réimse le do thoil.", "Please select a model first.": "", "Port": "Port", "Prefix ID": "Aitheantas Réimír", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Úsáidtear Aitheantas Réimír chun coinbhleachtaí le naisc eile a sheachaint trí réimír a chur le haitheantas na samhla - fág folamh le díchu<PERSON>ú", "Previous 30 days": "30 lá roimhe seo", "Previous 7 days": "7 lá roimhe seo", "Profile Image": "Íomhá Próifíl", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Pras (m.sh. inis dom fíric spra<PERSON><PERSON> faoin Impireacht Rómhánach)", "Prompt Content": "<PERSON><PERSON><PERSON>", "Prompt created successfully": "<PERSON>id cruthaithe go rathúil", "Prompt suggestions": "Moltaí pras", "Prompt updated successfully": "<PERSON>'<PERSON><PERSON>gh leis an leid a nuashonrú", "Prompts": "<PERSON><PERSON><PERSON>", "Prompts Access": "Rochtain ar <PERSON>", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Tarraing \"{{searchValue}}\" ó Ollama.com", "Pull a model from Ollama.com": "Tarraing múnla ó Ollama.com", "Query Generation Prompt": "<PERSON><PERSON><PERSON><PERSON> Ceisteanna", "Query Params": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RAG Template": "Teimpléad RAG", "Rating": "<PERSON><PERSON><PERSON><PERSON>", "Re-rank models by topic similarity": "Athrangaigh múnlaí de réir cosúlachta topaicí", "Read Aloud": "<PERSON><PERSON><PERSON>", "Record voice": "<PERSON><PERSON><PERSON>", "Redirecting you to OpenWebUI Community": "Tú a atreorú chuig OpenWebUI Community", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "Laghdaíonn sé an dóch<PERSON>acht go giniúint nonsense. Tabharfaidh luach níos airde (m.sh. 100) freagraí níos <PERSON>, agus beidh luach níos ísle (m.sh. 10) níos coimeádaí. (Réamhshocrú: 40)", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Tagairt duit féin mar \"Úsáideoir\" (m.sh., \"Tá an ú<PERSON>áideoir ag <PERSON>aim Spáinnis\")", "References from": "Tagairtí ó", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Release Notes": "<PERSON><PERSON><PERSON><PERSON>", "Relevance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Remove": "<PERSON><PERSON>", "Remove Model": "<PERSON><PERSON>", "Rename": "At<PERSON>nmni<PERSON>", "Reorder Models": "", "Repeat Last N": "<PERSON><PERSON><PERSON> an <PERSON>rea<PERSON>h arís", "Reply in Thread": "", "Request Mode": "<PERSON><PERSON><PERSON>", "Reranking Model": "<PERSON><PERSON><PERSON>", "Reranking model disabled": "<PERSON><PERSON><PERSON> faoi mh<PERSON>", "Reranking model set to \"{{reranking_model}}\"": "<PERSON><PERSON><PERSON> soc<PERSON>e go \"{{reranking_model}}\"", "Reset": "Athshocraigh", "Reset All Models": "", "Reset Upload Directory": "Athshocraigh Eolaire Uas", "Reset Vector Storage/Knowledge": "Athshocraigh Stóráil/Eolas Veicteoir", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Ní féidir fógra<PERSON> freagartha a ghníomhachtú toisc gur diúlta<PERSON>dh ceadanna an tsuímh <PERSON>. <PERSON><PERSON><PERSON> cuairt ar do shocruithe brabhsálaí chun an rochtain r<PERSON>h a dheonú.", "Response splitting": "Scoilt freagartha", "Result": "<PERSON><PERSON><PERSON>", "Retrieval Query Generation": "", "Rich Text Input for Chat": "<PERSON><PERSON><PERSON>", "RK": "RK", "Role": "R<PERSON>l", "Rosé Pine": "Pine Rosé", "Rosé Pine Dawn": "Rose Pine Dawn", "RTL": "RTL", "Run": "<PERSON><PERSON>", "Running": "Ag rith", "Save": "S<PERSON>bháil", "Save & Create": "Sábháil & Cruthaigh", "Save & Update": "Sábháil & Nuashonraigh", "Save As Copy": "Sábháil Mar <PERSON>", "Save Tag": "Sábháil Clib", "Saved": "Shábháil", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "<PERSON>í thacaítear le logaí comhrá a shábháil go dí<PERSON>ch chuig stóráil do bhrabhsálaí Tóg nóiméad chun do logaí comhrá a íoslódáil agus a scriosadh trí chliceáil an cnaipe thíos. Ná bíodh imní ort, is féidir leat do logaí comhrá a athiompórtáil go héasca chuig an gcúltaca trí", "Scroll to bottom when switching between branches": "Scrollaigh go bun agus tú ag athrú idir brainsí", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Search a model": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "Search Base": "Bonn Cuardaigh", "Search Chats": "Cuardaigh Comhráite", "Search Collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search Filters": "<PERSON><PERSON><PERSON><PERSON>", "search for tags": "cuardach le haghaidh clibeanna", "Search Functions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search Knowledge": "<PERSON><PERSON><PERSON><PERSON>", "Search Models": "<PERSON><PERSON><PERSON><PERSON>", "Search options": "<PERSON><PERSON><PERSON> cuardaigh", "Search Prompts": "<PERSON><PERSON><PERSON>", "Search Result Count": "<PERSON><PERSON><PERSON>", "Search the web": "Cuardaigh an gré<PERSON>án", "Search Tools": "<PERSON><PERSON><PERSON><PERSON>", "Search users": "", "SearchApi API Key": "Eochair API SearchAPI", "SearchApi Engine": "Inneall SearchAPI", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "Ag cuardach \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> do \"{{searchQuery}}\"", "Searxng Query URL": "URL ceisteanna cuardaigh", "See readme.md for instructions": "<PERSON><PERSON><PERSON> readme.md le haghaidh treoracha", "See what's new": "Féach cad atá nua", "Seed": "Síol", "Select a base model": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunm<PERSON>", "Select a engine": "Roghnai<PERSON> inneall", "Select a function": "R<PERSON>hn<PERSON><PERSON> feidhm", "Select a group": "Roghnaigh grúpa", "Select a model": "<PERSON><PERSON><PERSON><PERSON><PERSON> samhail", "Select a pipeline": "Roghnaigh píblíne", "Select a pipeline url": "Roghnaigh url p<PERSON>e", "Select a tool": "Roghnaigh uirlis", "Select Engine": "Roghnaigh Inneall", "Select Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select model": "<PERSON><PERSON><PERSON><PERSON><PERSON> samhail", "Select only one model to call": "Roghnai<PERSON> ach samhail amh<PERSON>in le <PERSON>och", "Selected model(s) do not support image inputs": "<PERSON><PERSON> th<PERSON>nn sa<PERSON>hai<PERSON>(í) rog<PERSON>aithe le hionchur <PERSON>", "Semantic distance to query": "<PERSON>ad sh<PERSON>ach le fi<PERSON>rú", "Send": "Seol", "Send a message": "", "Send a Message": "Seol Teachtaireacht", "Send message": "<PERSON><PERSON>taireach<PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Seolann `stream_options: { include_usage: true }` san iarratas.\nTabharfaidh soláthraithe a fhaigheann tacaíocht faisnéis ú<PERSON>áide chomharthaí ar ais sa fhreagra nuair a bheidh sé socraithe.", "September": "<PERSON><PERSON>", "Serper API Key": "Serper API Eochair", "Serply API Key": "Eochair API Serply", "Serpstack API Key": "Eochair API Serpstack", "Server connection verified": "Ceangal freastalaí fíoraithe", "Set as default": "<PERSON><PERSON><PERSON><PERSON> mar r<PERSON><PERSON><PERSON><PERSON><PERSON>e", "Set CFG Scale": "Socraigh Scála CFG", "Set Default Model": "<PERSON><PERSON><PERSON><PERSON>", "Set embedding model": "<PERSON><PERSON><PERSON><PERSON> samhail le<PERSON>e", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON> samhail le<PERSON> (m.sh. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON> samhail at<PERSON> (m.sh. {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON><PERSON>", "Set Scheduler": "<PERSON><PERSON><PERSON><PERSON>", "Set Steps": "<PERSON><PERSON><PERSON><PERSON>", "Set Task Model": "<PERSON><PERSON><PERSON><PERSON>", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Socraigh líon na bhfeistí GPU a úsáidtear le haghaidh ríomh. Rialaíonn an rogha seo cé mhéad gléas GPU (má tá siad ar fáil) a úsáidtear chun iarratais isteach a phróiseáil. Is féidir leis an luach seo a mhéadú feabhas suntasach a chur ar fheidhmíocht do mhún<PERSON>í atá optamaithe le haghaidh luasghéarú GPU ach d’fhéadfadh go n-ídíonn siad níos mó cumhachta agus acmhainní GPU freisin.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Socraigh líon na s<PERSON>itheanna oibrithe a úsáidtear le haghaidh ríomh. Rialaíonn an rogha seo cé mhéad snáithe a úsáidtear chun iarratais a thagann isteach a phróiseáil i gcomhthráth. D'fhéadfadh méadú ar an luach seo feidhmíocht a fheabhsú faoi ualaí oibre comhairgeadra ard ach féadfaidh sé níos mó acmhainní LAP a úsáid freisin.", "Set Voice": "<PERSON><PERSON><PERSON><PERSON>", "Set whisper model": "<PERSON><PERSON><PERSON>gh múnla cogar", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "Socraíonn sé cé chomh fada siar is atá an tsamhail le breathnú siar chun athrá a chosc. (Réamhshocrú: 64, 0 = díchu<PERSON>aithe, -1 = num_ctx)", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "Socraíonn sé cé chomh láidir is féidir pionós a ghearradh ar athrá. Cuirfidh luach níos airde (m.sh., 1.5) pionós níos láidre ar athrá, agus beidh luach níos ísle (m.sh., 0.9) níos boige. (Réamhshocrú: 1.1)", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "Socraíonn sé an síol uimhir randamach a ú<PERSON><PERSON> le haghaidh g<PERSON>. Má shocraítear é seo ar uimhir shaini<PERSON>, gin<PERSON>dh an tsamhail an té<PERSON>s céanna don leid céanna. (Réamhshocrú: randamach)", "Sets the size of the context window used to generate the next token. (Default: 2048)": "Socraíonn sé méid na fuinneoige comhthéacs a úsáidtear chun an chéad chomhartha eile a ghiniúint. (Réamhshocrú: 2048)", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Socraíonn sé na stadanna le húsáid. <PERSON><PERSON>ir a thagtar ar an bpatrún seo, stopfaidh an LLM ag giniúint téacs agus ag filleadh. Is féidir patrúin stad iolracha a shocrú trí pharaiméadair stadanna iolracha a shonrú i gcomhad samhail.", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Settings saved successfully!": "So<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON> go rath<PERSON>il!", "Share": "Comhroinn", "Share Chat": "Comhroinn Comhrá", "Share to OpenWebUI Community": "Comhroinn le Pobal OpenWebUI", "Show": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Show \"What's New\" modal on login": "Tai<PERSON><PERSON><PERSON><PERSON> mó<PERSON> \"Cad atá Nua\" ar logáil isteach", "Show Admin Details in Account Pending Overlay": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa <PERSON> ar Feitheamh Forleagan", "Show shortcuts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Show your support!": "Taispeáin do thacaíocht!", "Sign in": "<PERSON><PERSON><PERSON> is<PERSON>", "Sign in to {{WEBUI_NAME}}": "<PERSON><PERSON><PERSON> isteach ar {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "<PERSON><PERSON><PERSON> isteach ar {{WEBUI_NAME}} le LDAP", "Sign Out": "<PERSON><PERSON><PERSON>", "Sign up": "Cláraigh", "Sign up to {{WEBUI_NAME}}": "Cláraigh le {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "Ag sín<PERSON> isteach ar {{WEBUI_NAME}}", "sk-1234": "", "Source": "Foinse", "Speech Playback Speed": "<PERSON><PERSON>", "Speech recognition error: {{error}}": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>s cainte: {{error}}", "Speech-to-Text Engine": "<PERSON><PERSON><PERSON>-go-T<PERSON><PERSON><PERSON>", "Stop": "Stad", "Stop Sequence": "Stop Seicheamh", "Stream Chat Response": "<PERSON><PERSON><PERSON>", "STT Model": "Múnla STT", "STT Settings": "Socruithe STT", "Success": "<PERSON><PERSON>", "Successfully updated.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> go rathúil.", "Suggested prompts to get you started": "", "Support": "Taca<PERSON>cht", "Support this plugin:": "<PERSON><PERSON><PERSON> leis an m<PERSON>án seo:", "Sync directory": "Eolaire sioncronaithe", "System": "Córas", "System Instructions": "Treoracha <PERSON>", "System Prompt": "Córas Pras", "Tags Generation": "", "Tags Generation Prompt": "Clibeanna <PERSON>", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "Úsáidtear sampláil saor ó eireabaill chun tionchar na n-chomharthaí ón aschur nach bhfuil chomh dóchúil céanna a laghdú. Laghdóidh luach níos airde (m.sh., 2.0) an tionchar níos mó, agus díchu<PERSON>nn luach 1.0 an socrú seo. (réamhshocraithe: 1)", "Tap to interrupt": "Tapáil chun cur isteach", "Tavily API Key": "Eochair API Tavily", "Temperature": "Teocht", "Template": "Teimpléad", "Temporary Chat": "<PERSON><PERSON><PERSON><PERSON>", "Text Splitter": "Scoilteoir Téacs", "Text-to-Speech Engine": "Inneall téacs-go-labhra", "Tfs Z": "TFS Z", "Thanks for your feedback!": "Go raibh maith agat as do chuid a<PERSON>olas!", "The Application Account DN you bind with for search": "An Cuntas Feidhmchláir DN a nascann tú leis le haghaidh cuardaigh", "The base to search for users": "An bonn chun cuardach a dh<PERSON>ana<PERSON>h ar ú<PERSON>oirí", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "<PERSON><PERSON><PERSON><PERSON> méid an bhaisc cé mhéad iarratas téacs a phróiseáiltear le chéile ag an am céanna. Is féidir le méid baisc níos airde feidhmíocht agus luas an mhúnla a mhéadú, ach éilíonn sé níos mó cuimhne freisin. (Réamhshocrú: 512)", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Is deonacha paiseanta ón bpobal iad na forbróirí taobh thiar den bhreiseán seo. <PERSON><PERSON> aimsíonn an breiseán seo cabhrach leat, sma<PERSON><PERSON>gh ar rannchuid<PERSON> lena fhor<PERSON>.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Tá an clár ceannairí meastóireachta bunaithe ar chóras rátála Elo agus déantar é a nuashonrú i bhfíor-am.", "The LDAP attribute that maps to the username that users use to sign in.": "An tréith LDAP a mhapálann don ainm úsáideora a úsáideann úsáideoirí chun síniú isteach.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Tá an clár cean<PERSON> i b<PERSON>ite fao<PERSON> l<PERSON>, agus d'fhéadfaimis na río<PERSON>hanna rá<PERSON> a choigeartú de réir mar a dh<PERSON><PERSON><PERSON><PERSON>id an t-algartam a bheachtú.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Uasmhéid an chomhaid i MB. Má sháraíonn méid an chomhaid an teorainn seo, ní uasl<PERSON>dófar an comhad.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "An líon uasta na gcomhaid is féidir a úsáid ag an am céanna i gcomhrá. Má sháraíonn líon na gcomhaid an teorainn seo, ní uaslódófar na comhaid.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Ba chóir go mbeadh an scór ina luach idir 0.0 (0%) agus 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "Teocht an mhúnla. Déanfaidh méadú ar an teocht an freagra múnla níos cruthaithí. (Réamhshocrú: 0.8)", "Theme": "Téama", "Thinking...": "Smaointeoireacht...", "This action cannot be undone. Do you wish to continue?": "Ní féidir an gníomh seo a chur ar ais. Ar mhaith leat leanúint ar aghaidh?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Cinntíonn sé seo go s<PERSON><PERSON>hálfar do chomhráite luachmhara go daingean i do bhunachar sonraí cúltaca Go raibh maith agat!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Is gné turg<PERSON>ch í seo, b'fh<PERSON><PERSON><PERSON> nach bh<PERSON><PERSON><PERSON><PERSON><PERSON>h sé mar a bh<PERSON>thas ag súil leis agus tá sé faoi réir athraithe ag am ar bith.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "<PERSON><PERSON><PERSON><PERSON><PERSON> an rogha seo cé mhéad comhartha a chaomhnaítear agus an comhthéacs á athnuachan. Mar shampla, má shocraítear go 2 é, coinneofar an 2 chomhartha dheireanacha de chomhthéacs an chomhrá. Is féidir le comhthéacs a chaomhnú cabhrú le leanúnachas comhrá a choinneáil, ach d’fhéadfadh sé laghdú a dhéanamh ar an gcumas freagairt do thopaicí nua. (Réamhshocrú: 24)", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "Socra<PERSON>nn an rogha seo an t-uaslíon comhartha<PERSON> is féidir leis an tsamhail a ghiniúint ina fhreagra. Tríd an teorainn seo a mhéadú is féidir leis an tsamhail freagraí níos faide a shol<PERSON><PERSON>r, ach d’fhéadfadh go méadódh sé an dóchúlacht go nginfear ábhar neamhchabhrach nó nach mbaineann le hábhar. (Réamhshocrú: 128)", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "<PERSON><PERSON>s<PERSON><PERSON><PERSON> an rogha seo gach comhad atá sa bhailiú<PERSON>án agus cuirfear comhaid nua-uaslód<PERSON>la ina n-ionad.", "This response was generated by \"{{model}}\"": "Gin an freagra seo ag \"{{model}}\"", "This will delete": "Scriosfaid<PERSON> sé seo", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sé seo <strong>{{NAME}}</strong> agus <strong>a bhfuil ann go léir</strong>.", "This will delete all models including custom models": "Scriosfaidh sé seo gach múnla lena n-áirítear samhlacha saincheaptha", "This will delete all models including custom models and cannot be undone.": "Scriosfaidh sé seo gach samhail lena n-áirítear samhlacha saincheaptha agus ní féidir é a chealú.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sé seo an bonn eolais a athshocrú agus gach comhad a shioncronú. Ar mhaith leat leanúint ar aghaidh?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Teastaíonn URL Freastalaí Tika.", "Tiktoken": "T<PERSON><PERSON>", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Leid: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> slio<PERSON><PERSON><PERSON> i<PERSON> athróg as a chéile trí bhrú ar an eochair cluaisín san ionchur comhrá tar éis gach athshol<PERSON>thair.", "Title": "Teideal", "Title (e.g. Tell me a fun fact)": "Teideal (m.sh. inis dom fíric spra<PERSON>)", "Title Auto-Generation": "Teideal Auto-Generation", "Title cannot be an empty string.": "<PERSON><PERSON> féidir leis an teideal a bheith ina teaghrán folamh.", "Title Generation Prompt": "Pras Giniúint Teideal", "TLS": "TLS", "To access the available model names for downloading,": "<PERSON> teacht ar na hainm<PERSON>cha múnla atá ar fáil le híoslódáil,", "To access the GGUF models available for downloading,": "Chun rochtain a fháil ar na múnlaí GGUF atá ar fáil le híoslódáil,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Chun rochtain a fháil ar an WebUI, déan teagmháil leis an riarthóir le do thoil. Is féidir le riarthóirí stádas ú<PERSON>áideora a bhainistiú ón bPainéal Riaracháin.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "<PERSON> an bonn eolais a cheangal anseo, cuir leis an spás oibre \"E<PERSON>s\" iad ar dtús.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Chun do phríobháideachas a chosaint, ní roinntear ach r<PERSON>, a<PERSON><PERSON><PERSON><PERSON> mh<PERSON>, clibeanna agus meiteashonraí ó d<PERSON>a<PERSON>ola<PERSON> - fanann do logaí comhrá príobháideach agus níl siad san á<PERSON>.", "To select actions here, add them to the \"Functions\" workspace first.": "Chun gníom<PERSON><PERSON> a r<PERSON><PERSON><PERSON> an<PERSON>o, cuir iad leis an spás oibre \"Feidhmeanna\" ar dtús.", "To select filters here, add them to the \"Functions\" workspace first.": "Chun scagairí a r<PERSON> an<PERSON>o, cuir iad leis an spás oibre \"Feidhmeanna\" ar dtús.", "To select toolkits here, add them to the \"Tools\" workspace first.": "<PERSON> trealamh uirlis<PERSON> a r<PERSON> an<PERSON>o, cuir iad leis an spás oibre \"Uirlisí\" ar dtús.", "Toast notifications for new updates": "Fógraí tósta le haghaidh nuashonruithe nua", "Today": "<PERSON><PERSON>", "Toggle settings": "<PERSON><PERSON><PERSON><PERSON> soc<PERSON>", "Toggle sidebar": "At<PERSON>igh barra taobh", "Token": "<PERSON><PERSON><PERSON><PERSON>", "Tokens To Keep On Context Refresh (num_keep)": "Comharthaí le Coinneáil ar Athnuachan Comhthéacs (num_keep)", "Tool created successfully": "<PERSON><PERSON><PERSON> cruthaithe go rath<PERSON>il", "Tool deleted successfully": "Uirlis scriosta go rathúil", "Tool Description": "<PERSON>ur síos ar an <PERSON>lis", "Tool ID": "ID Uirlis", "Tool imported successfully": "<PERSON><PERSON><PERSON>", "Tool Name": "<PERSON><PERSON>", "Tool updated successfully": "An uirlis nuashonraithe", "Tools": "Uirlisí", "Tools Access": "Rochtain Uirlisí", "Tools are a function calling system with arbitrary code execution": "Is córas glaonna feidhme iad uirlisí le forghníomhú cód treallach", "Tools have a function calling system that allows arbitrary code execution": "Tá córas glaonna feidhme ag uirlis<PERSON> a cheadaíonn forghníomhú cód treallach", "Tools have a function calling system that allows arbitrary code execution.": "Tá córas glaonna feidhme ag uirlis<PERSON> a chead<PERSON>íonn forghníomhú cód treallach.", "Top K": "Barr K", "Top P": "Barr P", "Transformers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON><PERSON> teacht ar <PERSON>?", "TTS Model": "TTS Múnla", "TTS Settings": "Socruithe TTS", "TTS Voice": "Guth TTS", "Type": "Cineál", "Type Hugging Face Resolve (Download) URL": "Cineál Hugging Face Resolve (Íoslódáil) URL", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "Díchartlannaigh Uile", "Unarchive All Archived Chats": "Díchartlannaigh Gach Comhrá <PERSON>", "Unarchive Chat": "Comhrá a dhícha<PERSON>lannú", "Unlock mysteries": "Díghlasáil rúndiamhra", "Unpin": "Díphoráil", "Unravel secrets": "<PERSON><PERSON><PERSON> a r<PERSON>ach", "Untagged": "<PERSON><PERSON> ch<PERSON>b", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Nuashonraigh agus Cóipeáil Nasc", "Update for the latest features and improvements.": "Nuashonr<PERSON> le haghaidh na gn<PERSON>ithe agus na feab<PERSON>he is déanaí.", "Update password": "Nuashonrú pasfhocal", "Updated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated at": "Nuashonraithe ag", "Updated At": "Nuashonraithe Ag", "Upload": "Uaslódáil", "Upload a GGUF model": "Uaslódáil samhail GGUF", "Upload directory": "Uaslódáil eolaire", "Upload files": "Uaslódáil comhaid", "Upload Files": "Uaslódáil Comhaid", "Upload Pipeline": "Uaslódáil píblíne", "Upload Progress": "Uaslód<PERSON> an Dul", "URL": "URL", "URL Mode": "<PERSON><PERSON><PERSON> U<PERSON>", "Use '#' in the prompt input to load and include your knowledge.": "Úsáid '#' san ionchur pras chun do chuid eolais a lódáil agus a chur san á<PERSON>am<PERSON>.", "Use Gravatar": "Ús<PERSON>id Gravatar", "Use groups to group your users and assign permissions.": "Úsáid grúpa<PERSON> chun d'úsáideoirí a ghrúpáil agus ceadanna a shannadh", "Use Initials": "<PERSON><PERSON><PERSON><PERSON>", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "úsáideoir", "User": "Úsáideoir", "User location successfully retrieved.": "<PERSON>arthas <PERSON>h an ú<PERSON>áideora go rathú<PERSON>.", "Username": "Ainm <PERSON>", "Users": "Úsáideoirí", "Using the default arena model with all models. Click the plus button to add custom models.": "Ag baint ú<PERSON><PERSON><PERSON> as an múnla réimse réamhsho<PERSON>raithe le gach múnlaí. Cliceáil ar an gcnaipe móide chun múnlaí saincheaptha a chur leis.", "Utilize": "Úsáid", "Valid time units:": "<PERSON><PERSON><PERSON> ama bailí:", "Valves": "Comhlaí", "Valves updated": "Comhl<PERSON><PERSON> d<PERSON>ta", "Valves updated successfully": "Comhlaí nuashonraíodh", "variable": "<PERSON><PERSON><PERSON><PERSON>", "variable to have them replaced with clipboard content.": "athr<PERSON>g chun ábhar gearrthaisce a chur in ionad iad.", "Version": "<PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "Leagan {{selectedVersion}} de {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "<PERSON>f<PERSON><PERSON><PERSON>cht", "Voice": "<PERSON><PERSON>", "Voice Input": "<PERSON><PERSON><PERSON>", "Warning": "Ra<PERSON><PERSON>", "Warning:": "Rabhadh:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Rabhadh: <PERSON><PERSON><PERSON><PERSON><PERSON> sé seo ar chumas ú<PERSON>áideoirí cód treallach a uaslódáil ar an bhfreastalaí.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Rabhadh: <PERSON><PERSON> nuash<PERSON>íonn tú nó má athraíonn tú do mhúnla le<PERSON>e, beidh ort gach doici<PERSON>ad a athiompórtáil.", "Web": "Gréasán", "Web API": "API Gréasáin", "Web Loader Settings": "<PERSON><PERSON><PERSON><PERSON>", "Web Search": "<PERSON><PERSON><PERSON>", "Web Search Engine": "<PERSON><PERSON><PERSON>", "Web Search Query Generation": "", "Webhook URL": "URL Webhook", "WebUI Settings": "Socruithe WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "Déanfaidh WebUI iarratais ar \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "Déanfaidh WebUI iarratais ar \"{{url}}/chat/completions\"", "What are you trying to achieve?": "Cad atá tú ag iarraidh a bhaint amach?", "What are you working on?": "Cad air a bhfuil tú ag obair?", "What didn't you like about this response?": "", "What’s New in": "Cad atá Nua i", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON>uair a bheidh sé cumasaithe, freag<PERSON><PERSON><PERSON>h an tsamhail gach teachtaireacht comhrá i bhfíor-am, ag giniúint freagra a luaithe a sheolann an t-úsáideoir teachtaireacht. Tá an mód seo ú<PERSON>áideach le haghaidh feidhmchláir chomhrá beo, ach d’fhéadfadh tionchar a bheith aige ar fheidhmíocht ar chrua-earraí níos moille.", "wherever you are": "aon áit a bhfuil tú", "Whisper (Local)": "Whisper (Áitiúil)", "Widescreen Mode": "<PERSON><PERSON><PERSON>", "Won": "Bhuaigh", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "Oibríonn sé le barr-<PERSON><PERSON> <PERSON><PERSON><PERSON> téacs níos <PERSON> mar thoradh ar luach níos airde (m.sh., 0.95), agus ginfidh luach níos <PERSON> (m.sh., 0.5) téacs níos dírithe agus níos co<PERSON>da<PERSON>. (Réamhshocrú: 0.9)", "Workspace": "Spás oibre", "Workspace Permissions": "Ceadanna Spás Oibre", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON><PERSON><PERSON> moladh pras (m.sh. <PERSON>é hé tú?)", "Write a summary in 50 words that summarizes [topic or keyword].": "<PERSON>r<PERSON><PERSON><PERSON> achoimre i 50 focal a dh<PERSON>anann achoimre ar [á<PERSON>r nó eochairfhocal].", "Write something...": "Scríobh rud...", "Write your model template content here": "Scríobh do mhúnla ábhar teimpléad anseo", "Yesterday": "<PERSON><PERSON>", "You": "Tú", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON>í féidir leat comhrá a dhéanamh ach le comhad {{maxCount}} ar a mhéad ag an am.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Is féidir leat do chuid idirghníomhaíochtaí le LLManna a phearsantú ach cuimhní cinn a chur leis tríd an gcnaipe 'Bainistigh' th<PERSON><PERSON>, rud a fhágann go mbeidh siad níos cabhrach agus níos oiri<PERSON>aí duit.", "You cannot upload an empty file.": "Ní féidir leat comhad folamh a uaslódáil.", "You have no archived conversations.": "N<PERSON>l aon chomhráite cartlainne agat.", "You have shared this chat": "Tá an comhrá seo roinnte agat", "You're a helpful assistant.": "Is cúnt<PERSON>ir cabhrach tú.", "Your account status is currently pending activation.": "Tá stádas do chuntais ar feitheamh faoi ghníomhachtú.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Rachaidh do ranníocaíocht iomlán go dí<PERSON>ch chuig an bhforbróir brei<PERSON>; Ní <PERSON>nn Open WebUI aon chéatadán. <PERSON> sin féin, d'<PERSON>héadfadh a tháillí féin a bheith ag an ardán maoinithe roghnaithe.", "Youtube": "Youtube", "Youtube Loader Settings": "<PERSON><PERSON><PERSON><PERSON> Youtube"}