{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' alebo '-1' pre žiadne vypršanie platnosti.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(napr. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(napr. `sh webui.sh --api`)", "(latest)": "Najnov<PERSON><PERSON>", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}'s konver<PERSON><PERSON><PERSON>", "{{webUIName}} Backend Required": "Vyžaduje sa {{webUIName}} Backend", "*Prompt node ID(s) are required for image generation": "*Sú potrebné IDs pre prompt node na generovanie obr<PERSON><PERSON>kov", "A new version (v{{LATEST_VERSION}}) is now available.": "Nová verzia (v{{LATEST_VERSION}}) je teraz k dispozícii.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Model úloh sa používa pri vykoná<PERSON>í <PERSON>, ako je generovanie názvov pre chaty a vyhľadávacie dotazy na webe.", "a user": "užívateľ", "About": "O programe", "Access": "Prístup", "Access Control": "", "Accessible to all users": "Prístupné pre všetkých užívateľov", "Account": "Účet", "Account Activation Pending": "Čaká sa na aktiváciu účtu", "Actions": "<PERSON><PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktivujte tento príkaz napísaním \"/{{COMMAND}}\" do chatového vstupu", "Active Users": "Aktívni užívatelia", "Add": "Pridať", "Add a model ID": "Pridať ID modelu", "Add a short description about what this model does": "Pridajte kr<PERSON>ky popis toho, čo tento model rob<PERSON>.", "Add a tag": "Pridať štítok", "Add Arena Model": "Pridať Arena model", "Add Connection": "Pridať pripojenie", "Add Content": "Pridať obsah", "Add content here": "Pridať obsah sem", "Add custom prompt": "Pridanie vlastného promptu", "Add Files": "Pridať súbory", "Add Group": "Pridať skupinu", "Add Memory": "Pridať pamäť", "Add Model": "Pridať model", "Add Reaction": "", "Add Tag": "Pridať štítok", "Add Tags": "Pridať štítky", "Add text content": "Pridajte textový obsah", "Add User": "Pridať užívateľa", "Add User Group": "Pridať skupinu užívateľov", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Úprava týchto nastavení sa prejaví univerzálne u všetkých užívateľov.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Admin panel", "Admin Settings": "Nastavenia admina", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administrátori majú prístup ku všetkým nástrojom kedykoľvek; užívatelia potrebujú mať nástroje priradené podľa modelu v workspace.", "Advanced Parameters": "Pokročilé parametre", "Advanced Params": "Pokročilé parametre", "All Documents": "Všetky dokumenty", "All models deleted successfully": "Všetky modely úspešne odstránené", "Allow Chat Delete": "Povoliť odstránenie chatu", "Allow Chat Deletion": "Povoliť odstránenie chatu", "Allow Chat Edit": "Povoliť úpravu chatu", "Allow File Upload": "Povoliť nahrávanie súborov", "Allow non-local voices": "Povoliť ne-lokálne hlasy", "Allow Temporary Chat": "Povoliť dočasný chat", "Allow User Location": "Povoliť užívateľskú polohu", "Allow Voice Interruption in Call": "Povoliť prerušenie hlasu počas hovoru", "Allowed Endpoints": "", "Already have an account?": "<PERSON><PERSON> máte účet?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "asistent", "and": "a", "and {{COUNT}} more": "a {{COUNT}} ď<PERSON>šie/í", "and create a new shared link.": "a vytvoriť nový zdieľaný odkaz.", "API Base URL": "Základná URL adresa API", "API Key": "API kľúč", "API Key created.": "API kľúč bol vytvorený.", "API Key Endpoint Restrictions": "", "API keys": "API kľúče", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "Apr<PERSON><PERSON>", "Archive": "Archivovať", "Archive All Chats": "Archivovať všetky konverzácie", "Archived Chats": "Archivované k<PERSON>z<PERSON>", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Ste si istý?", "Arena Models": "Arena modely", "Artifacts": "Artefakty", "Ask a question": "Opýtajte sa otázku", "Assistant": "Asistent", "Attach file": "Pripojiť súbor", "Attribute for Username": "", "Audio": "Zvuk", "August": "August", "Authenticate": "Autentifikovať", "Auto-Copy Response to Clipboard": "Automatické kopírovanie odpovede do schránky", "Auto-playback response": "Automatická odpoveď pri prehrávaní", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "Základná URL pre AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Vyžaduje sa základná URL pre AUTOMATIC1111.", "Available list": "Dostupný zoznam", "available!": "k dispozícii!", "Azure AI Speech": "Azure AI syntéza reči", "Azure Region": "Azure oblasť", "Back": "Späť", "Bad": "", "Bad Response": "<PERSON><PERSON><PERSON>z<PERSON>", "Banners": "<PERSON><PERSON>", "Base Model (From)": "<PERSON><PERSON><PERSON><PERSON><PERSON> model (z)", "Batch Size (num_batch)": "Veľkosť batchu (num_batch)", "before": "pred", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "API kľúč pre Brave Search", "By {{name}}": "", "Bypass SSL verification for Websites": "Obísť overenie SSL pre web<PERSON><PERSON> str<PERSON>", "Call": "Volanie", "Call feature is not supported when using Web STT engine": "Funkcia volania nie je podporovaná pri použití Web STT engine.", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Zrušiť", "Capabilities": "Sc<PERSON>nosti", "Capture": "", "Certificate Path": "", "Change Password": "Zmeniť heslo", "Channel Name": "", "Channels": "", "Character": "Znak", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Cha<PERSON>", "Chat Background Image": "Obrázok pozadia chatu", "Chat Bubble UI": "Používateľské rozhranie bublín chatu (Chat Bubble UI)", "Chat Controls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> chatu", "Chat direction": "<PERSON><PERSON> chatu", "Chat Overview": "<PERSON><PERSON><PERSON><PERSON> chatu", "Chat Permissions": "", "Chat Tags Auto-Generation": "Automatické generovanie značiek chatu", "Chats": "<PERSON><PERSON>", "Check Again": "Skontroluj znovu", "Check for updates": "Skontrolovať aktualizácie", "Checking for updates...": "Kontrola aktualizácií...", "Choose a model before saving...": "Vyberte model pred ul<PERSON>ž<PERSON>...", "Chunk Overlap": "", "Chunk Params": "", "Chunk Size": "", "Ciphers": "", "Citation": "Odkaz", "Clear memory": "Vymazať pamäť", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Kliknite tu pre pomoc.", "Click here to": "Kliknite tu na", "Click here to download user import template file.": "Kliknite tu pre stiahnutie šablóny súboru na import užívateľov.", "Click here to learn more about faster-whisper and see the available models.": "Kliknite sem a dozviete sa viac o faster-whisper a pozrite si dostupné modely.", "Click here to select": "Kliknite sem pre výber", "Click here to select a csv file.": "Kliknite sem pre výber súboru typu csv.", "Click here to select a py file.": "Kliknite sem pre výber {{py}} súboru.", "Click here to upload a workflow.json file.": "Kliknite sem pre nahratie súboru workflow.json.", "click here.": "kliknite tu.", "Click on the user role button to change a user's role.": "Kliknite na tlačidlo role užívateľa, aby ste zmenili rolu užívateľa.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Prístup na zápis do schránky bol zamietnutý. Skontrolujte nastavenia prehliadača a udeľte potrebný prístup.", "Clone": "Klonovať", "Close": "Zavrieť", "Code execution": "Vykonávanie kódu", "Code formatted successfully": "<PERSON><PERSON><PERSON> bol úspešne naformátovaný.", "Collection": "", "Color": "Farba", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "Základná URL ComfyUI", "ComfyUI Base URL is required.": "Je vyžadovaná základná URL pre ComfyUI.", "ComfyUI Workflow": "Pracovný postup ComfyUI", "ComfyUI Workflow Nodes": "Pracovné uzly ComfyUI", "Command": "Príkaz", "Completions": "Doplnenia", "Concurrent Requests": "Súčasné p<PERSON>ž<PERSON>", "Configure": "Konfigurovať", "Configure Models": "Konfigurovať modely", "Confirm": "Potvrdiť", "Confirm Password": "Potvrdenie hesla", "Confirm your action": "Potvrďte svoju akciu", "Confirm your new password": "", "Connections": "Pripojenia", "Contact Admin for WebUI Access": "Kontaktujte administrátora pre prístup k webovému rozhraniu.", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction": "Extrakcia obsahu", "Context Length": "Dĺžka kontextu", "Continue Response": "Pokračovať v odpovedi", "Continue with {{provider}}": "Pokračovať s {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON><PERSON><PERSON>, ako sa text správy rozdeľuje pre požiadavky TTS. 'Punctuation' rozdeľuje text na vety, 'paragraphs' rozdeľuje text na odseky a 'none' ponecháva správu ako jeden celý reťazec.", "Controls": "Ovládacie prvky", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "Skopírované", "Copied shared chat URL to clipboard!": "URL zdieľanej konverzácie skopírované do schránky!", "Copied to clipboard": "Skopírované do schránky", "Copy": "Kopírovať", "Copy last code block": "Skopírujte posledný blok kódu", "Copy last response": "Skopírujte poslednú odpoveď", "Copy Link": "Kopírovať odkaz", "Copy to clipboard": "Kopírovať do schránky", "Copying to clipboard was successful!": "Kopírovanie do schránky bolo úspešné!", "Create": "Vytvoriť", "Create a knowledge base": "Vytvoriť knowledge base", "Create a model": "Vytvoriť model", "Create Account": "Vytvoriť účet", "Create Admin Account": "Vytvoriť admin <PERSON>", "Create Channel": "", "Create Group": "Vytvoriť skupinu", "Create Knowledge": "Vytvoriť knowledge", "Create new key": "Vytvoriť nový kľúč", "Create new secret key": "Vytvoriť nový tajný kľúč", "Created at": "Vytvor<PERSON><PERSON>", "Created At": "Vytvor<PERSON><PERSON>", "Created by": "Vytvorené užívateľom", "CSV Import": "CSV import", "Current Model": "Aktuálny model", "Current Password": "Aktuáln<PERSON> he<PERSON>", "Custom": "Na mieru", "Dark": "Tmavý", "Database": "Databáza", "December": "December", "Default": "Predvolené hodnoty alebo nastavenia.", "Default (Open AI)": "<PERSON>d<PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Predvolené (SentenceTransformers)", "Default Model": "Predvolený model", "Default model updated": "Predvolený model a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "Default Models": "Predvolené modely", "Default permissions": "Predvolené povolenia", "Default permissions updated successfully": "Predvolené povolenia úspešne aktualizované", "Default Prompt Suggestions": "Predvolen<PERSON> n<PERSON> promptov", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Predvolená rola užívateľa", "Delete": "Odstrániť", "Delete a model": "Odstrániť model.", "Delete All Chats": "Odstrániť všetky konverzácie", "Delete All Models": "", "Delete chat": "Odstrániť chat", "Delete Chat": "Odstrániť chat", "Delete chat?": "Odstrániť konverzáciu?", "Delete folder?": "Odstrániť priečinok?", "Delete function?": "Funkcia na odstránenie?", "Delete Message": "", "Delete prompt?": "Odstrániť prompt?", "delete this link": "odstrániť tento odkaz", "Delete tool?": "Odstrániť nástroj?", "Delete User": "Odstrániť užívateľa", "Deleted {{deleteModelTag}}": "Odstránené {{deleteModelTag}}", "Deleted {{name}}": "Odstránené {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "<PERSON><PERSON>", "Disabled": "Zak<PERSON><PERSON><PERSON>", "Discover a function": "Objaviť funkciu", "Discover a model": "Objaviť model", "Discover a prompt": "Objaviť prompt", "Discover a tool": "Objaviť nástroj", "Discover wonders": "", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sťahujte a preskúmajte vlastné funk<PERSON>", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON>, stiahnite a preskúmajte vlastné prompty.", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sťahujte a preskúmajte vlastné n<PERSON>", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON><PERSON>, stiahnite a preskúmajte prednastavenia modelov", "Dismissible": "Odstrániteľné", "Display": "", "Display Emoji in Call": "Zobrazenie emoji počas hovoru", "Display the username instead of You in the Chat": "Zobraziť užívateľské meno namiesto \"Vás\" v chate", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "Neinštalujte funkcie zo zdr<PERSON>jov, ktorým plne nedôverujete.", "Do not install tools from sources you do not fully trust.": "Neinštalujte nástroje zo zdr<PERSON>jov, ktorým plne nedôverujete.", "Document": "Dokument", "Documentation": "Dokumentácia", "Documents": "Dokumenty", "does not make any external connections, and your data stays securely on your locally hosted server.": "nevytvára žiadne externé pripojenia a vaše dáta zostávajú bezpečne na vašom lokálnom serveri.", "Don't have an account?": "<PERSON>emáte <PERSON>?", "don't install random functions from sources you don't trust.": "Neinštalujte náhodné funkcie zo zdrojov, ktor<PERSON>m nedôverujete.", "don't install random tools from sources you don't trust.": "Neinštalujte náhodné nástroje zo zdr<PERSON>jov, ktorým nedôverujete.", "Done": "Hotovo.", "Download": "Stiahnuť", "Download canceled": "Sťahovanie zrušené", "Download Database": "Stiahnuť databázu", "Drag and drop a file to upload or select a file to view": "", "Draw": "Nakresliť", "Drop any files here to add to the conversation": "Sem presuňte akékoľvek súbory, ktor<PERSON> chcete pridať do konverzácie", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "napr. '30s','10m'. <PERSON><PERSON><PERSON><PERSON> j<PERSON> sú 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Upraviť", "Edit Arena Model": "Upraviť Arena Model", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "Upraviť pamäť", "Edit User": "Upraviť užívateľa", "Edit User Group": "", "ElevenLabs": "ElevenLabs", "Email": "E-mail", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "Vkladací model (Embedding Model)", "Embedding Model Engine": "", "Embedding model set to \"{{embedding_model}}\"": "Model vkladania nastavený na \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Povoliť zdieľanie komunity", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "Povoliť hodnotenie správ", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Povoliť nové registrácie", "Enable Web Search": "Povoliť webové vyhľadávanie", "Enabled": "Povolené", "Engine": "Engine", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Uistite sa, že váš CSV súbor obsahuje 4 stĺpce v tomto poradí: Name, Email, Password, Role.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON><PERSON> spr<PERSON> {{role}} sem", "Enter a detail about yourself for your LLMs to recall": "Zadajte podrobnosť o sebe, ktor<PERSON> si vaše LLM majú z<PERSON>mätať.", "Enter api auth string (e.g. username:password)": "Zadajte autentifikačný reťazec API (napr. užívateľské_meno:heslo)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Zadajte API kľúč pre Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "Zadajte mierku CFG (napr. 7.0)", "Enter Chunk Overlap": "Zadajte prekryv časti", "Enter Chunk Size": "Zadajte veľkosť časti", "Enter description": "Zadaj<PERSON> popis", "Enter Github Raw URL": "Zadajte URL adresu <PERSON>", "Enter Google PSE API Key": "Zadajte kľúč rozhrania API Google PSE", "Enter Google PSE Engine Id": "Zadajte ID vyhľadávacieho mechanizmu Google PSE", "Enter Image Size (e.g. 512x512)": "Zadajte veľkosť obrázka (napr. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Zadajte kó<PERSON>", "Enter Model ID": "Zadajte ID modelu", "Enter model tag (e.g. {{modelTag}})": "Zadajte označenie modelu (napr. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Zadajte po<PERSON><PERSON> krokov (napr. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "Zada<PERSON><PERSON> (napr. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON><PERSON> (napr. <PERSON><PERSON><PERSON>)", "Enter Score": "Zadajte skóre", "Enter SearchApi API Key": "Zadajte API kľúč pre SearchApi", "Enter SearchApi Engine": "Zadajte vyhľadávací engine SearchApi", "Enter Searxng Query URL": "Zadajte URL dopytu Searxng", "Enter Seed": "", "Enter Serper API Key": "Zadajte Serper API kľúč", "Enter Serply API Key": "Zadajte API kľúč pre Serply", "Enter Serpstack API Key": "Zadajte kľúč API pre Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Zadajte ukončovaciu sekvenciu", "Enter system prompt": "<PERSON><PERSON><PERSON><PERSON> systémový prompt", "Enter Tavily API Key": "Zadajte API kľúč <PERSON>", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Zadajte URL servera Tika", "Enter Top K": "<PERSON><PERSON><PERSON><PERSON>", "Enter URL (e.g. http://127.0.0.1:7860/)": "Zadajte URL (napr. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Zadajte URL (napr. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Zadajte svoj email", "Enter Your Full Name": "Zadajte svoje celé meno", "Enter your message": "Zadajte svoju správu", "Enter your new password": "", "Enter Your Password": "Zadajte svoje heslo", "Enter your prompt": "", "Enter Your Role": "Zadajte svoju rolu", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Chyba", "ERROR": "Chyba", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Hodnotenia", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "Vylúčiť", "Experimental": "Experiment<PERSON>lne", "Explore the cosmos": "", "Export": "Exportovať", "Export All Archived Chats": "", "Export All Chats (All Users)": "Exportovať všetky konverzácie (všetci užívatelia)", "Export chat (.json)": "Exportovať konverzáciu (.json)", "Export Chats": "Exportovať konverzácie", "Export Config to JSON File": "Exportujte konfiguráciu do súboru JSON", "Export Functions": "Exportovať funkcie", "Export Models": "Exportovať modely", "Export Presets": "", "Export Prompts": "Exportovať prompty", "Export to CSV": "", "Export Tools": "Export<PERSON><PERSON>", "External Models": "Externé modely", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "Nepodarilo sa pridať súbor.", "Failed to create API Key.": "Nepodarilo sa vytvoriť API kľúč.", "Failed to read clipboard contents": "Nepodarilo sa prečítať obsah s<PERSON>ránky", "Failed to save models configuration": "", "Failed to update settings": "Nepodarilo sa aktualizovať nastavenia", "February": "<PERSON><PERSON><PERSON><PERSON>", "Feedback History": "História spätnej väzby", "Feedbacks": "", "File": "S<PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON><PERSON> bol úsp<PERSON>š<PERSON> p<PERSON>ý.", "File content updated successfully.": "<PERSON><PERSON><PERSON> s<PERSON>u bol úspešne aktualizovaný.", "File Mode": "<PERSON><PERSON><PERSON>", "File not found.": "<PERSON><PERSON><PERSON>.", "File removed successfully.": "Súbor bol úspešne odstránený.", "File size should not exceed {{maxSize}} MB.": "Veľkosť súboru by nemal<PERSON> {{maxSize}} MB.", "File uploaded successfully": "", "Files": "Súbory", "Filter is now globally disabled": "Filter je teraz globálne z<PERSON>ý", "Filter is now globally enabled": "<PERSON>lter je teraz globálne povolený.", "Filters": "Filtre", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Zistené falš<PERSON>nie odtlačkov prstov: Nie je možné použiť iniciály ako avatar. Používa sa predvolený profilový obrázok.", "Fluidly stream large external response chunks": "Plynule streamujte veľké externé časti odpovedí", "Focus chat input": "Zamerajte sa na vstup chatu", "Folder deleted successfully": "Priečinok bol úspešne vymazaný", "Folder name cannot be empty": "Názov priečinka nesmie byť prázdny", "Folder name cannot be empty.": "Názov priečinka nesmie byť prázdny.", "Folder name updated successfully": "Názov priečinka bol úspešne aktualizovaný.", "Forge new paths": "", "Form": "<PERSON><PERSON><PERSON><PERSON>", "Format your variables using brackets like this:": "Formátujte svoje premenné pomocou zátvoriek takto:", "Frequency Penalty": "Penalizácia frekvencie", "Function": "Funkcia", "Function created successfully": "Funkcia bola úspešne vytvorená.", "Function deleted successfully": "Funkcia bola úspešne odstránená", "Function Description": "", "Function ID": "", "Function is now globally disabled": "Funkcia je teraz globálne zakázaná.", "Function is now globally enabled": "Funkcia je teraz globálne povolená.", "Function Name": "", "Function updated successfully": "Funkcia bola úspešne aktualizovaná.", "Functions": "Funkcie", "Functions allow arbitrary code execution": "Funkcie umožňujú vykonávať ľubovoľný kód.", "Functions allow arbitrary code execution.": "Funkcie umožňujú vykonávanie ľubovoľného kódu.", "Functions imported successfully": "Funkcie boli úspešne <PERSON>ovan<PERSON>", "General": "Všeobecné", "General Settings": "Všeobecné nastavenia", "Generate Image": "Vygenerovať obrázok", "Generating search query": "Generovanie vyhľadávacieho dotazu", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Globálne", "Good Response": "Dobrá odozva", "Google Drive": "", "Google PSE API Key": "Kľúč API pre Google PSE (Programmatically Search Engine)", "Google PSE Engine Id": "Google PSE Engine Id (Identifikátor vyhľadávacieho modulu Google PSE)", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "hh:mm dop./odp.", "Haptic Feedback": "Haptická spätná väzba", "Harmful or offensive": "", "has no conversations.": "nemá žiadne konverzácie.", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "Pomoc", "Help us create the best community leaderboard by sharing your feedback history!": "Pomôžte nám vytvoriť najlepší komunitný rebríček zdieľaním histórie vašej spätnej väzby!", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Skryť", "Host": "", "How can I help you today?": "Ako vám môžem dnes pomôcť?", "How would you rate this response?": "", "Hybrid Search": "Hybrid<PERSON>é v<PERSON>ľadávanie", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Beriem na vedomie, že som si prečítal a chápem dôsledky svojich činov. Som si vedomý rizík spojených s vykonávaním ľubovoľného kódu a overil som dôveryhodnosť zdroja.", "ID": "ID", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON> (experimentálne)", "Image Generation Engine": "Engine na generovanie obrázkov", "Image Max Compression Size": "", "Image Settings": "Nastavenia obrázka", "Images": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Chats": "Importovať konverzácie", "Import Config from JSON File": "Importovanie konfigurácie z JSON súboru", "Import Functions": "Načítanie funkcií", "Import Models": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>ov", "Import Presets": "", "Import Prompts": "Importovať Prompty", "Import Tools": "Importovať nástroje", "Include": "Zahrnúť", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON>ň<PERSON> prepínač `--api-auth` pri spustení stable-diffusion-webui.", "Include `--api` flag when running stable-diffusion-webui": "Pri spustení stable-diffusion-webui zahrňte príznak `--api`.", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Info", "Input commands": "Vstupné príkazy", "Install from Github URL": "Inštalácia z URL adresy Githubu", "Instant Auto-Send After Voice Transcription": "Okamžité automatické odoslanie po prepisu hlasu", "Interface": "<PERSON><PERSON><PERSON><PERSON>", "Invalid file format.": "Neplatný form<PERSON>.", "Invalid Tag": "Neplatný tag", "is typing...": "", "January": "<PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "pripojte sa k nášmu Discordu pre pomoc.", "JSON": "JSON", "JSON Preview": "Náhľad JSON", "July": "<PERSON><PERSON>", "June": "Jún", "JWT Expiration": "Vypršanie platnosti JWT (JSON Web Token)", "JWT Token": "JWT <PERSON> (JSON Web Token)", "Kagi Search API Key": "", "Keep Alive": "Udržiavať spojenie", "Key": "", "Keyboard shortcuts": "Klávesov<PERSON> skratky", "Knowledge": "Znalosti", "Knowledge Access": "", "Knowledge created successfully.": "Znalosť úspešne vytvorená.", "Knowledge deleted successfully.": "Znalosti boli úspešne odstránené.", "Knowledge reset successfully.": "Úspešné obnovenie znalostí.", "Knowledge updated successfully": "Znalosti úspešne aktualizované", "Label": "", "Landing Page Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Language": "Jazyk", "Last Active": "Naposledy aktívny", "Last Modified": "Posledná zmena", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Leave empty for unlimited": "Nechajte prázdne pre neobmedzene", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Nechajte prázdne pre zahrnutie všetkých modelov alebo vyberte konkrétne modely.", "Leave empty to use the default prompt, or enter a custom prompt": "Nechajte prázdne pre použitie predvoleného podnetu, alebo zadajte vlastný podnet.", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Počúvanie...", "Local": "", "Local Models": "<PERSON><PERSON><PERSON><PERSON> modely", "Lost": "Stratený", "LTR": "LTR", "Made by OpenWebUI Community": "Vytvorené komunitou OpenWebUI", "Make sure to enclose them with": "Uistite sa, že sú uzavreté pomocou", "Make sure to export a workflow.json file as API format from ComfyUI.": "Uistite sa, že exportujete súbor workflow.json vo formáte API z ComfyUI.", "Manage": "Spravovať", "Manage Arena Models": "Správa modelov v Arena", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Správa pipelines", "March": "<PERSON><PERSON>", "Max Tokens (num_predict)": "Maximálny po<PERSON> (num_predict)", "Max Upload Count": "Maximálny počet nahraní", "Max Upload Size": "Maximálna veľkosť nahrávania", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maximálne 3 modely mô<PERSON>u byť stiahnuté súčasne. Prosím skúste to znova neskôr.", "May": "<PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "<PERSON><PERSON><PERSON>nky prístupné LLM budú zobrazené tu.", "Memory": "Pamäť", "Memory added successfully": "Pamäť bola úspešne pridaná.", "Memory cleared successfully": "Pamäť bola úspešne vymazaná.", "Memory deleted successfully": "Pamäť bola úspešne vymazaná", "Memory updated successfully": "Pamäť úspešne aktualizovaná", "Merge Responses": "Zlúčiť odpovede", "Message rating should be enabled to use this feature": "Hodnotenie správ musí byť povolené, aby bolo možné túto funkciu p<PERSON>ží<PERSON>.", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> po vytvorení o<PERSON>, nebu<PERSON><PERSON> zdieľané. Používatelia s URL budú môcť zobraziť zdieľaný chat.", "Min P": "<PERSON>", "Minimum Score": "Minimálne skóre", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, RRRR", "MMMM DD, YYYY HH:mm": "MMMM DD, RRRR HH:mm", "MMMM DD, YYYY hh:mm:ss A": "MMMM DD, YYYY hh:mm:ss A", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "Model „{{modelName}}“ bol <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' je u<PERSON> z<PERSON>den<PERSON> do fronty na sťahovanie.", "Model {{modelId}} not found": "Model {{modelId}} nebol <PERSON>", "Model {{modelName}} is not vision capable": "Model {{modelName}} nie je schopný spracovávať vizuálne údaje.", "Model {{name}} is now {{status}}": "Model {{name}} je teraz {{status}}.", "Model accepts image inputs": "Model prij<PERSON><PERSON> vs<PERSON><PERSON> vo forme obr<PERSON><PERSON>kov", "Model created successfully!": "Model bol úspešne vytvorený!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Zistená cesta v súborovom systéme. Je vyžadovaný krátky názov modelu pre aktualizáciu, ne<PERSON>žno pokračovať.", "Model Filtering": "", "Model ID": "ID modelu", "Model IDs": "", "Model Name": "Názov modelu", "Model not selected": "Model nebol v<PERSON>", "Model Params": "Parametre modelu", "Model Permissions": "", "Model updated successfully": "Model bol <PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>ual<PERSON>", "Modelfile Content": "<PERSON><PERSON><PERSON> modelfile", "Models": "<PERSON><PERSON>", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "viac", "More": "Viac", "Name": "<PERSON><PERSON>", "Name your knowledge base": "", "New Chat": "<PERSON><PERSON> chat", "New folder": "", "New Password": "<PERSON><PERSON>", "new-channel": "", "No content found": "Nebol nájdený žiadny obsah.", "No content to speak": "Žiadny obsah na diskusiu.", "No distance available": "<PERSON>e je dostupná žiadna vzdialenosť", "No feedbacks found": "Žiadna spätná väzba nenájdená", "No file selected": "Nebola vybratá žiadna súbor", "No files found.": "Neboli nájdené žiadne súbory.", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "Nebola nájdená žiadny obsah HTML, CSS ani JavaScript.", "No knowledge found": "Neboli nájdené žiadne znalosti", "No model IDs": "", "No models found": "Neboli nájdené žiadne modely", "No models selected": "", "No results found": "Neboli nájdené žiadne výsledky", "No search query generated": "Nebola vygenerovaná žiadna vyhľadávacia otázka.", "No source available": "<PERSON>e je dostupný žiadny zdroj.", "No users were found.": "", "No valves to update": "Žiadne ventily na aktualizáciu", "None": "Žiadny", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Poznámka: Ak nastavíte minimálne skóre, vyhľadávanie vráti iba dokumenty s hodnotením, ktoré je väčšie alebo rovné zadanému minimálnemu skóre.", "Notes": "Poznámky", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Oznámenia", "November": "November", "num_gpu (Ollama)": "Počet GPU (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "Október", "Off": "Vypnuté", "Okay, Let's Go!": "<PERSON><PERSON>, poďme na to!", "OLED Dark": "OLED Dark", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "API rozhranie <PERSON> je zakázané.", "Ollama API settings updated": "", "Ollama Version": "<PERSON><PERSON><PERSON>", "On": "Zapnut<PERSON>", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Príkazový reťazec môže obsahovať iba alfanumerické znaky a pomlčky.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON><PERSON> kole<PERSON> môžu byť upravované, na úpravu/pridanie dokumentov vytvorte novú znalostnú databázu.", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Jejda! Vyzerá to, že URL adresa je neplatná. Prosím, skontrolujte ju a skúste to znova.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Jejda! Niektoré súbory sa stále nahrávajú. Prosím, p<PERSON><PERSON><PERSON><PERSON><PERSON>, kým sa nahrávanie dokončí.", "Oops! There was an error in the previous response.": "Jejda! V predchádzajúcej odpovedi došlo k chybe.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Jejda! Používate nepodporovanú metódu (iba frontend). Prosím, spustite WebUI zo serverovej časti (backendu).", "Open in full screen": "Otvoriť na celú obrazovku", "Open new chat": "Otvoriť nový chat", "Open WebUI uses faster-whisper internally.": "Open WebUI interne používa faster-whisper.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Verzia Open WebUI (v{{OPEN_WEBUI_VERSION}}) je nižšia ako požadovan<PERSON> verzia (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI je výskumná organizácia zameraná na umelú inteligenciu, ktorá je známa vývojom pokročilých jazykových modelov, ako je napríklad GPT. Tieto modely sa využívajú v rôznych aplikáciách, vrátane konverzačných agentov a jazykových nástrojov.", "OpenAI API": "OpenAI API je rozhranie aplikačného programovania, ktoré umožňuje vývojárom integrovať pokročilé jazykové modely do svojich aplikácií.", "OpenAI API Config": "Konfigurácia API OpenAI", "OpenAI API Key is required.": "Je vyžadovaný kľúč OpenAI API.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "Je vyžadovaný odkaz/adresa URL alebo kľúč OpenAI.", "or": "alebo", "Organize your users": "", "OUTPUT": "VÝSTUP", "Output format": "Formát výstupu", "Overview": "Prehľad", "page": "<PERSON><PERSON><PERSON><PERSON>", "Password": "He<PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF dokument (.pdf)", "PDF Extract Images (OCR)": "Extrahovanie obrázkov z PDF (OCR)", "pending": "čaká na vybavenie", "Permission denied when accessing media devices": "Odmietnutie povolenia pri prístupe k mediálnym zariadeniam", "Permission denied when accessing microphone": "Prístup k mikrofónu bol zamietnutý", "Permission denied when accessing microphone: {{error}}": "Oprávnenie zamietnuté pri prístupe k mikrofónu: {{error}}", "Permissions": "", "Personalization": "Personalizácia", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "Pipeline bola úspešne odstránená", "Pipeline downloaded successfully": "<PERSON><PERSON><PERSON> bol <PERSON><PERSON>", "Pipelines": "", "Pipelines Not Detected": "<PERSON><PERSON><PERSON><PERSON> kanály ne<PERSON>i de<PERSON>", "Pipelines Valves": "", "Plain text (.txt)": "Čistý text (.txt)", "Playground": "", "Please carefully review the following warnings:": "Prosí<PERSON>, pozorne si prečítajte nasledujúce upozornenia:", "Please enter a prompt": "Prosím, zadajte zadanie.", "Please fill in all fields.": "Prosím, vyplňte všetky polia.", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Predchádzajúcich 30 dní", "Previous 7 days": "Predchádzajúcich 7 dní", "Profile Image": "Profilový obrázok", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (napr. Povedz mi zábavnú skutočnosť o Rímskej ríši)", "Prompt Content": "<PERSON><PERSON><PERSON> promptu", "Prompt created successfully": "", "Prompt suggestions": "Návrhy výziev", "Prompt updated successfully": "", "Prompts": "Prompty", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Stiahnite \"{{searchValue}}\" z Ollama.com", "Pull a model from Ollama.com": "Stiahnite model z Ollama.com", "Query Generation Prompt": "", "Query Params": "Parametre dotazu", "RAG Template": "Šablóna RAG", "Rating": "Hodnotenie", "Re-rank models by topic similarity": "Znova zoradiť modely podľa podobnosti tém.", "Read Aloud": "Čítať nahlas", "Record voice": "Nahrať hlas", "Redirecting you to OpenWebUI Community": "Presmerovanie na komunitu OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Odkazujte na seba ako na \"užívateľa\" (napr. \"Užívateľ sa učí španielsky\").", "References from": "Referencie z", "Refresh Token Expiration": "", "Regenerate": "Regenerovať", "Release Notes": "Záznamy o vydaní", "Relevance": "Relevancia", "Remove": "Odstrániť", "Remove Model": "Odstrániť model", "Rename": "Premenovať", "Reorder Models": "", "Repeat Last N": "Opakovať posledných N", "Reply in Thread": "", "Request Mode": "<PERSON><PERSON><PERSON>", "Reranking Model": "Model na prehodnotenie poradia", "Reranking model disabled": "Model na prehodnotenie poradia je deaktivovaný", "Reranking model set to \"{{reranking_model}}\"": "Model na prehodnotenie poradia nastavený na \"{{reranking_model}}\"", "Reset": "<PERSON><PERSON><PERSON>", "Reset All Models": "", "Reset Upload Directory": "Resetovať adresár <PERSON>", "Reset Vector Storage/Knowledge": "Resetovanie úložiska vektorov/znalostí", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Oznámenia o odpovediach nie je možné aktivovať, pretože povolenia webu boli zamietnuté. Navštívte nastavenia svojho prehliadača a povoľte potrebný prístup.", "Response splitting": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "Result": "Výsledok", "Retrieval Query Generation": "", "Rich Text Input for Chat": "Vstup pre chat vo formáte Rich Text", "RK": "RK", "Role": "Funkcia", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Spustiť", "Running": "Spúšťanie", "Save": "Uložiť", "Save & Create": "Uložiť a Vytvoriť", "Save & Update": "Uložiť a aktualizovať", "Save As Copy": "Uložiť ako kópiu", "Save Tag": "Uložiť štítok", "Saved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Ukladanie záznamov chatu priamo do úložiska vášho prehliadača už nie je podporované. Venujte prosím chvíľu stiahnutiu a vymazaniu svojich záznamov chatu kliknutím na tlačidlo nižšie. Nemajte obavy, môžete ľahko znovu importovať svoje záznamy chatu na backend prostredníctvom", "Scroll to bottom when switching between branches": "Prejsť na koniec pri prepínaní medzi vetvami.", "Search": "Vyhľadávanie", "Search a model": "Vyhľadať model", "Search Base": "", "Search Chats": "Vyhľadávanie v chate", "Search Collection": "Hľadať kolekciu", "Search Filters": "", "search for tags": "hľadanie značiek", "Search Functions": "Vyhľadávacie funkcie", "Search Knowledge": "Vyhľadávanie znalostí", "Search Models": "Vyhľadávacie modely", "Search options": "", "Search Prompts": "Vyhľadávacie dotazy", "Search Result Count": "Počet výsledkov hľadania", "Search the web": "", "Search Tools": "Nástroje na vyhľadávanie", "Search users": "", "SearchApi API Key": "Kľúč API pre SearchApi", "SearchApi Engine": "Vyhľadávací engine API", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Vyhľadávanie znalostí pre \"{{searchQuery}}\"", "Searxng Query URL": "Adresa URL dotazu Searxng", "See readme.md for instructions": "Pozrite si {{readme.md}} pre pokyny.", "See what's new": "Pozrite sa, čo je nové", "Seed": "<PERSON><PERSON>", "Select a base model": "<PERSON><PERSON><PERSON><PERSON> model", "Select a engine": "Vyberte engine", "Select a function": "<PERSON><PERSON><PERSON><PERSON>", "Select a group": "", "Select a model": "Vyberte model", "Select a pipeline": "Vyberte pipeline", "Select a pipeline url": "Vyberte URL adresu kanála", "Select a tool": "<PERSON><PERSON><PERSON><PERSON>", "Select Engine": "Vyberte engine", "Select Knowledge": "Vybrať znalosti", "Select model": "Vyberte model", "Select only one model to call": "<PERSON><PERSON><PERSON><PERSON> <PERSON> j<PERSON> model, k<PERSON><PERSON> chcete použi<PERSON>", "Selected model(s) do not support image inputs": "<PERSON>y<PERSON><PERSON>ý(é) model(y) nepodporujú vstupy v podobe obrázkov.", "Semantic distance to query": "Sémantická vzdialenosť k dotazu", "Send": "Odoslať", "Send a message": "", "Send a Message": "Odoslať správu", "Send message": "Odoslať správu", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "O<PERSON><PERSON><PERSON> `stream_options: { include_usage: true }` v žiadosti. Podporovaní poskytovatelia vrátia informácie o využití tokenov v odpovedi, keď je táto možnosť nastavená.", "September": "September", "Serper API Key": "Kľúč API pre Serper", "Serply API Key": "Serply API kľúč", "Serpstack API Key": "Kľúč API pre Serpstack", "Server connection verified": "Pripo<PERSON><PERSON> k <PERSON>u overené", "Set as default": "Nastaviť ako predvolené", "Set CFG Scale": "Nastavte hodnotu CFG Scale", "Set Default Model": "Nastavenie predvoleného modelu", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Nastavte model vkladania (napr. {{model}})", "Set Image Size": "Nastavenie veľkosti obrázku", "Set reranking model (e.g. {{model}})": "Nastavte model na prehodnotenie (napr. {{model}})", "Set Sampler": "Nastavenie vzorkovača", "Set Scheduler": "Nastavenie plánovača", "Set Steps": "Nastavenie krokov", "Set Task Model": "Nastaviť model <PERSON><PERSON><PERSON>", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Nastaviť hlas", "Set whisper model": "Nastaviť model whisper", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Nastavenia", "Settings saved successfully!": "Nastavenia boli úspešne uložené!", "Share": "Zdieľať", "Share Chat": "Zdieľať chat", "Share to OpenWebUI Community": "Zdieľať s komunitou OpenWebUI", "Show": "Zobraziť", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Zobraziť podrobnosti administrátora v prekryvnom okne s čakajúcim účtom", "Show shortcuts": "Zobraziť klávesové skratky", "Show your support!": "Vyjadrite svoju podporu!", "Sign in": "Prihlásiť sa", "Sign in to {{WEBUI_NAME}}": "Prihlásiť sa do {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Odhlásiť sa", "Sign up": "Zaregistrovať sa", "Sign up to {{WEBUI_NAME}}": "Zaregistrujte sa na {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "P<PERSON>hlasovanie do {{WEBUI_NAME}}", "sk-1234": "", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "Rýchlosť prehrávania reči", "Speech recognition error: {{error}}": "Chyba rozpoznávania reči: {{error}}", "Speech-to-Text Engine": "Motor prevodu reči na text", "Stop": "Zastaviť", "Stop Sequence": "Sekvencia zastavenia", "Stream Chat Response": "Odozva chatu Stream", "STT Model": "Model rozpoznávania reči na text (STT)", "STT Settings": "Nastavenia STT (Rozpoznávanie reči)", "Success": "Úspech", "Successfully updated.": "Úspešne aktualizované.", "Suggested prompts to get you started": "", "Support": "Podpora", "Support this plugin:": "Podporte tento plugin:", "Sync directory": "Synchronizovať adresár", "System": "Systém", "System Instructions": "", "System Prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prompt", "Tags Generation": "", "Tags Generation Prompt": "Prompt na generovanie značiek", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "Klepnite na prerušenie", "Tavily API Key": "Kľúč API pre Tavily", "Temperature": "", "Template": "Šablóna", "Temporary Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON> chat", "Text Splitter": "Rozdeľovač textu", "Text-to-Speech Engine": "Stroj na prevod textu na reč", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Ďakujeme za vašu spätnú väzbu!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Vývojári stojaci za týmto pluginom sú zapálení dobrovoľníci z komunity. Ak považujete tento plugin za užitočný, zvážte príspevok na jeho vývoj.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Hodnotiaca tabuľka je založená na systéme hodnotenia Elo a aktualizuje sa v reálnom čase.", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Hodnotiaca tabuľka je momentálne v beta verzii a môžeme upraviť výpočty hodnotenia, ako budeme zdokonaľovať algoritmus.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Maximálna veľkosť súboru v MB. Ak veľkosť súboru presiahne tento limit, súbor nebude nahraný.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Maximálny počet súborov, ktor<PERSON> je možné použiť naraz v chate. Ak počet súborov presiahne tento limit, súbory nebudú nahrané.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "<PERSON><PERSON><PERSON><PERSON> by malo by<PERSON> hodnotou medzi 0,0 (0%) a 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON><PERSON>", "Thinking...": "Pre<PERSON><PERSON><PERSON><PERSON><PERSON>...", "This action cannot be undone. Do you wish to continue?": "<PERSON><PERSON><PERSON> akciu nie je možné vrátiť späť. Prajete si pokrač<PERSON>ť?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Tý<PERSON><PERSON> je zaistené, že vaše cenné konverzácie sú bezpečne uložené vo vašej backendovej databáze. Ďakujeme!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Toto je <PERSON>, nemusí fungovať podľa očakávania a môže byť kedykoľvek zmenená.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Táto voľba odstráni všetky existujúce súbory v kolekcii a nahradí ich novo nahranými súbormi.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON><PERSON> odpoveď bola vygenerovaná pomocou \"{{model}}\"", "This will delete": "<PERSON><PERSON>", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> k ods<PERSON> <strong>{{NAME}}</strong> a <strong>v<PERSON><PERSON><PERSON><PERSON><PERSON> jeho obsahov</strong>.", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Toto obnoví znalostnú databázu a synchronizuje všetky súbory. Prajete si pokračovať?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Je vyžadovaná URL adresa servera Tika.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tip: Aktualizujte postupne viacero premenných slotov stlačením klávesy Tab v chate po každej náhrade.", "Title": "N<PERSON>zov", "Title (e.g. Tell me a fun fact)": "Názov (napr. Povedz mi zaujímavosť)", "Title Auto-Generation": "<PERSON><PERSON><PERSON> generovani<PERSON> n<PERSON>", "Title cannot be an empty string.": "Názov nemôže byť prázdny reťazec.", "Title Generation Prompt": "Generovanie názvu promptu", "TLS": "", "To access the available model names for downloading,": "Pre získanie dostupných názvov modelov na stiahnutie,", "To access the GGUF models available for downloading,": "Pre prístup k modelom GGUF dostupným na stiahnutie,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Pre prístup k WebUI sa prosím obráťte na administrátora. Administrátori môžu spravovať stavy používateľov z Admin Panelu.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Ak chcete tu pripojiť znalostnú databázu, najprv ju pridajte do pracovného priestoru \"Knowledge\".", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Na ochranu vášho súkromia sú z vašej spätnej väzby zdieľané iba hodnotenia, ID modelov, znač<PERSON> a metadáta – vaše záznamy chatu zostávajú súkromné a nie sú zahrnuté.", "To select actions here, add them to the \"Functions\" workspace first.": "Ak chcete tu vybrať akcie, najprv ich pridajte do pracovného priestoru \"Functions\".", "To select filters here, add them to the \"Functions\" workspace first.": "Ak chcete tu vybrať filtre, najprv ich pridajte do pracovného priestoru „Functions“.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Ak chcete tu vybrať nástroje, pridajte ich najprv do pracovného priestoru \"Tools\".", "Toast notifications for new updates": "Oznámenia vo forme toastov pre nové aktualizácie", "Today": "Dnes", "Toggle settings": "Prepnúť nastavenia", "Toggle sidebar": "Prepnúť boč<PERSON>ý panel", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "<PERSON><PERSON><PERSON>, ktoré si ponechať pri obnovení kontextu (num_keep)", "Tool created successfully": "Nástroj bol úspešne vytvorený.", "Tool deleted successfully": "Nás<PERSON>j bol úspešne odstránený.", "Tool Description": "", "Tool ID": "ID nástroja", "Tool imported successfully": "<PERSON><PERSON><PERSON>j bol úsp<PERSON><PERSON>", "Tool Name": "", "Tool updated successfully": "Nás<PERSON>j bol úspešne aktualizovaný.", "Tools": "<PERSON>ás<PERSON><PERSON>", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "Nástroje sú systémom na volanie funkcií s vykonávaním ľubovoľného kódu.", "Tools have a function calling system that allows arbitrary code execution": "Nástroje majú systém volania funkcií, k<PERSON>ý umož<PERSON><PERSON><PERSON>ovoľné spúšťanie kódu.", "Tools have a function calling system that allows arbitrary code execution.": "Nástroje majú systém volania funkcií, ktorý umožňuje spúšťanie ľubovoľného kódu.", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON> problémy s prístupom k Ollama?", "TTS Model": "Model prevodu textu na reč (TTS)", "TTS Settings": "Nastavenia TTS (Text-to-Speech)", "TTS Voice": "TTS hlas", "Type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Zadajte URL na úspešné stiahnutie z Hugging Face.", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "Odzálohovať všetky", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "Odopnúť", "Unravel secrets": "", "Untagged": "Nebola oz<PERSON>čená", "Update": "Aktualizovať", "Update and Copy Link": "Aktualizovať a skopírovať odkaz", "Update for the latest features and improvements.": "Aktualizácia pre najnovšie funkcie a vylepšenia.", "Update password": "Aktualizovať heslo", "Updated": "Aktualizované", "Updated at": "Aktualizované <PERSON>a", "Updated At": "Aktualizované <PERSON>a", "Upload": "Nahrať", "Upload a GGUF model": "Nahrať model vo formáte GGUF", "Upload directory": "Nahrať adres<PERSON>", "Upload files": "Nahrať súbory", "Upload Files": "Nahrať súbory", "Upload Pipeline": "Nahrať pipeline", "Upload Progress": "Priebeh nahrávania", "URL": "", "URL Mode": "<PERSON><PERSON><PERSON>", "Use '#' in the prompt input to load and include your knowledge.": "Použite '#' vo vstupe promptu na načítanie a zahrnutie vašich vedomostí.", "Use Gravatar": "Použiť Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "Použiť iniciály", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "použ<PERSON><PERSON>ľ", "User": "Používateľ", "User location successfully retrieved.": "Umiestnenie používateľa bolo úspešne získané.", "Username": "Používateľské meno", "Users": "Používatelia", "Using the default arena model with all models. Click the plus button to add custom models.": "Používanie predvoleného modelu arény so všetkými modelmi. Kliknutím na tlačidlo plus pridajte vlastné modely.", "Utilize": "Využiť", "Valid time units:": "<PERSON><PERSON><PERSON><PERSON>:", "Valves": "Ventily", "Valves updated": "<PERSON><PERSON><PERSON> a<PERSON><PERSON>", "Valves updated successfully": "<PERSON><PERSON>ily boli úspešne aktualizované.", "variable": "premenná", "variable to have them replaced with clipboard content.": "premenn<PERSON>, aby bol ich obsah nahradený obsahom s<PERSON>ky.", "Version": "Verzia", "Version {{selectedVersion}} of {{totalVersions}}": "Verzia {{selectedVersion}} z {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "Viditeľnosť", "Voice": "<PERSON><PERSON>", "Voice Input": "Hlasový vstup", "Warning": "Varovanie", "Warning:": "Upozornenie:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Varovanie: Ak aktualizujete alebo zmeníte svoj model vkladania, budete musieť všetky dokumenty znovu importovať.", "Web": "Web", "Web API": "Webové API", "Web Loader Settings": "Nastavenia Web Loaderu", "Web Search": "Vyhľadávanie na webe", "Web Search Engine": "Webový vyhľadávač", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "Nastavenia WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Čo je nové v", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "kdekoľvek ste", "Whisper (Local)": "Whisper (Lokálne)", "Widescreen Mode": "<PERSON><PERSON><PERSON>ho zobraze<PERSON>", "Won": "Vyhral", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Navrhnite otázku (napr. <PERSON>to ste?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Napíšte zhrnutie na 50 slov, k<PERSON><PERSON> [tému alebo kľú<PERSON>ov<PERSON> slovo].", "Write something...": "Na<PERSON><PERSON><PERSON><PERSON> nie<PERSON>...", "Write your model template content here": "", "Yesterday": "Včera", "You": "Vy", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Môž<PERSON> komunikovať len s maximálne {{maxCount}} s<PERSON>bor(ami) naraz.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Môžete personalizovať svoje interakcie s LLM pridaním spomienok prostredníctvom tlačidla 'Spravovať' ni<PERSON><PERSON><PERSON>, čo ich urobí pre vás užitočnejšími a lepšie prispôsobenými.", "You cannot upload an empty file.": "Nemôžete nahrať prázdny súbor.", "You have no archived conversations.": "Nemáte žiadne archivované konverzácie.", "You have shared this chat": "<PERSON><PERSON><PERSON><PERSON> ste tento chat.", "You're a helpful assistant.": "Ste užitočný asistent.", "Your account status is currently pending activation.": "Stav vášho účtu je aktuálne čakajúci na aktiváciu.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Celý váš príspevok pôjde priamo vývojárovi pluginu; Open WebUI si neberie žiadne percento. Zvolená platforma na financovanie však môže mať vlastné poplatky.", "Youtube": "YouTube", "Youtube Loader Settings": "Nastavenia YouTube loaderu"}