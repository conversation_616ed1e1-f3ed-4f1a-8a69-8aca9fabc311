{"-1 for no limit, or a positive integer for a specific limit": "-1 за бесконачно или позитивни број за одређено ограничење", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "„s“, „m“, „h“, „d“, „w“ или „-1“ за без истека.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(нпр. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(нпр. `sh webui.sh --api`)", "(latest)": "(најновије)", "{{ models }}": "{{ модели }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Ћаскања корисника {{user}}", "{{webUIName}} Backend Required": "Захтева се {{webUIName}} позадинац", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Модел задатка се користи приликом извршавања задатака као што су генерисање наслова за ћаскања и упите за Веб претрагу", "a user": "корисник", "About": "О нама", "Access": "Приступ", "Access Control": "Контрола приступа", "Accessible to all users": "Доступно свим корисницима", "Account": "Налог", "Account Activation Pending": "Налози за активирање", "Actions": "Радње", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Покрените ову наредбу куцањем \"/{{COMMAND}}\" у ћаскање.", "Active Users": "Активни корисници", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "Додај ИБ модела", "Add a short description about what this model does": "Додавање кратког описа о томе шта овај модел ради", "Add a tag": "Дода<PERSON> ознаку", "Add Arena Model": "Додај модел Арене", "Add Connection": "Додај везу", "Add Content": "Додај садржај", "Add content here": "Додај садржај овде", "Add custom prompt": "Додај прилагођен упит", "Add Files": "Додај датотеке", "Add Group": "Дода<PERSON> групу", "Add Memory": "Додај меморију", "Add Model": "Додај модел", "Add Reaction": "", "Add Tag": "Дода<PERSON> ознаку", "Add Tags": "Додај ознаке", "Add text content": "Додај садржај текста", "Add User": "Дод<PERSON><PERSON> корисника", "Add User Group": "Додај корисничку групу", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Прилагођавање ових подешавања ће применити промене на све кориснике.", "admin": "админ", "Admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Admin Panel": "Ад<PERSON>ин табла", "Admin Settings": "Админ подешавања", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Админи имају приступ свим алатима у сваком тренутку, корисницима је потребно доделити алате по моделу у радном простору", "Advanced Parameters": "Напредни параметри", "Advanced Params": "Напредни парамови", "All Documents": "Сви документи", "All models deleted successfully": "Сви модели су успешно обрисани", "Allow Chat Delete": "Дозволи брисање ћаскања", "Allow Chat Deletion": "Дозволи брисање ћаскања", "Allow Chat Edit": "Дозволи измену ћаскања", "Allow File Upload": "Дозволи отпремање датотека", "Allow non-local voices": "Дозволи нелокалне гласове", "Allow Temporary Chat": "Дозволи привремена ћаскања", "Allow User Location": "Дозволи корисничку локацију", "Allow Voice Interruption in Call": "Дозволи прекид гласа у позиву", "Allowed Endpoints": "", "Already have an account?": "Већ имате налог?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "помоћник", "and": "и", "and {{COUNT}} more": "и још {{COUNT}}", "and create a new shared link.": "и направи нову дељену везу.", "API Base URL": "Основна адреса API-ја", "API Key": "API кључ", "API Key created.": "API кључ направљен.", "API Key Endpoint Restrictions": "", "API keys": "API кључеви", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "<PERSON><PERSON>рил", "Archive": "<PERSON>р<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "Архивирај сва ћаскања", "Archived Chats": "Архиве", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Да ли сте сигурни?", "Arena Models": "Модели са Арене", "Artifacts": "Артефакти", "Ask a question": "Постави питање", "Assistant": "Помоћник", "Attach file": "Приложи датотеку", "Attribute for Username": "Особина корисника", "Audio": "Звук", "August": "Август", "Authenticate": "Идентификација", "Auto-Copy Response to Clipboard": "Самостално копирање одговора у оставу", "Auto-playback response": "Самостално пуштање одговора", "Autocomplete Generation": "Стварање самодовршавања", "Autocomplete Generation Input Max Length": "Најдужи улаз стварања самодовршавања", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Automatic1111 Api ниска идентификације", "AUTOMATIC1111 Base URL": "Основна адреса за AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Потребна је основна адреса за AUTOMATIC1111.", "Available list": "Списак доступног", "available!": "доступно!", "Azure AI Speech": "Azure AI говор", "Azure Region": "Azure област", "Back": "Назад", "Bad": "", "Bad Response": "<PERSON><PERSON><PERSON> одговор", "Banners": "Барјаке", "Base Model (From)": "Основни модел (од)", "Batch Size (num_batch)": "", "before": "пре", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Апи кључ за храбру претрагу", "By {{name}}": "", "Bypass SSL verification for Websites": "Заобиђи SSL потврђивање за веб странице", "Call": "Позив", "Call feature is not supported when using Web STT engine": "", "Camera": "Камера", "Cancel": "Откажи", "Capabilities": "Могућности", "Capture": "", "Certificate Path": "", "Change Password": "Промени лозинку", "Channel Name": "", "Channels": "", "Character": "Знак", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Ћаскање", "Chat Background Image": "", "Chat Bubble UI": "Интерфејс балона ћаскања", "Chat Controls": "", "Chat direction": "Смер ћаскања", "Chat Overview": "Преглед ћаскања", "Chat Permissions": "Дозволе ћаскања", "Chat Tags Auto-Generation": "", "Chats": "Ћаскања", "Check Again": "Провери поново", "Check for updates": "Потра<PERSON>и ажурирања", "Checking for updates...": "Траже се ажурирања...", "Choose a model before saving...": "Изабери модел пре чувања...", "Chunk Overlap": "Преклапање делова", "Chunk Params": "Параметри делова", "Chunk Size": "Величина дела", "Ciphers": "", "Citation": "Цитат", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Кликните овде за помоћ.", "Click here to": "Кликните овде да", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Кликните овде да изаберете", "Click here to select a csv file.": "Кликните овде да изаберете csv датотеку.", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "кликните овде.", "Click on the user role button to change a user's role.": "Кликните на дугме за улогу корисника да промените улогу корисника.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "Клонир<PERSON><PERSON>", "Close": "Затвори", "Code execution": "", "Code formatted successfully": "", "Collection": "Колекција", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "Основна адреса за ComfyUI", "ComfyUI Base URL is required.": "Потребна је основна адреса за ComfyUI.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Наредба", "Completions": "", "Concurrent Requests": "Упоредни захтеви", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "Потврди лозинку", "Confirm your action": "", "Confirm your new password": "", "Connections": "Везе", "Contact Admin for WebUI Access": "", "Content": "Садржај", "Content Extraction": "", "Context Length": "<PERSON><PERSON><PERSON><PERSON><PERSON> контекста", "Continue Response": "Настави одговор", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "Контроле", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "Копирано", "Copied shared chat URL to clipboard!": "Адреса дељеног ћаскања ископирана у оставу!", "Copied to clipboard": "Копирано у оставу", "Copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "Copy last code block": "Копирај последњи блок кода", "Copy last response": "Копирај последњи одговор", "Copy Link": "Копирај везу", "Copy to clipboard": "", "Copying to clipboard was successful!": "Успешно копирање у оставу!", "Create": "Направи", "Create a knowledge base": "Направи базу знања", "Create a model": "Креирање модела", "Create Account": "Направи налог", "Create Admin Account": "Направи админ налог", "Create Channel": "", "Create Group": "Направи групу", "Create Knowledge": "Направи знање", "Create new key": "Направи нови кључ", "Create new secret key": "Направи нови тајни кључ", "Created at": "Направљено у", "Created At": "Направљено у", "Created by": "Направио/ла", "CSV Import": "", "Current Model": "Тренутни модел", "Current Password": "Тренутна лозинка", "Custom": "Прилагођено", "Dark": "Тамна", "Database": "База података", "December": "Децембар", "Default": "Подразумевано", "Default (Open AI)": "", "Default (SentenceTransformers)": "Подразумевано (SentenceTransformers)", "Default Model": "Подразумевани модел", "Default model updated": "Подразумевани модел ажуриран", "Default Models": "Подразумевани модели", "Default permissions": "Подразумевана овлашћења", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Подразумевани предлози упита", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Подразумевана улога корисника", "Delete": "Об<PERSON><PERSON><PERSON>и", "Delete a model": "Обриши модел", "Delete All Chats": "Избриши сва ћаскања", "Delete All Models": "", "Delete chat": "Обриши ћаскање", "Delete Chat": "Обриши ћаскање", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "обриши ову везу", "Delete tool?": "", "Delete User": "Обриши корисника", "Deleted {{deleteModelTag}}": "Обрисано {{deleteModelTag}}", "Deleted {{name}}": "Избрисан<PERSON> {{наме}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "<PERSON><PERSON><PERSON><PERSON>", "Disabled": "", "Discover a function": "Откријте функцију", "Discover a model": "Откријте модел", "Discover a prompt": "Отк<PERSON><PERSON>ј упит", "Discover a tool": "Откријте алат", "Discover wonders": "", "Discover, download, and explore custom functions": "Откријте, преузмите и истражите прилагођене функције", "Discover, download, and explore custom prompts": "Откријте, преузмите и истражите прилагођене упите", "Discover, download, and explore custom tools": "Откријте, преузмите и истражите прилагођене алате", "Discover, download, and explore model presets": "Откријте, преузмите и истражите образце модела", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "Прикажи корисничко име уместо Ти у чату", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Документ", "Documentation": "", "Documents": "Документи", "does not make any external connections, and your data stays securely on your locally hosted server.": "не отвара никакве спољне везе и ваши подаци остају сигурно на вашем локално хостованом серверу.", "Don't have an account?": "Немате налог?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "Преузми", "Download canceled": "Преузимање отказано", "Download Database": "Преузми базу података", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Убаците било које датотеке овде да их додате у разговор", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "нпр. '30s', '10m'. Важеће временске јединице су 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Уреди", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "Уреди корисника", "Edit User Group": "", "ElevenLabs": "", "Email": "Е-пошта", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "Модел уградње", "Embedding Model Engine": "Мотор модела уградње", "Embedding model set to \"{{embedding_model}}\"": "Модел уградње подешен на \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Омогући дељење заједнице", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Омогући нове пријаве", "Enable Web Search": "Омогући Wеб претрагу", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Уверите се да ваша CSV датотека укључује 4 колоне у овом редоследу: Име, Е-пошта, Лозинка, Улога.", "Enter {{role}} message here": "Унесите {{role}} поруку овде", "Enter a detail about yourself for your LLMs to recall": "Унесите детаље за себе да ће LLMs преузимати", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Унесите БРАВЕ Сеарцх АПИ кључ", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Унесите преклапање делова", "Enter Chunk Size": "Унесите величину дела", "Enter description": "", "Enter Github Raw URL": "Унесите Гитхуб Раw УРЛ адресу", "Enter Google PSE API Key": "Унесите Гоогле ПСЕ АПИ кључ", "Enter Google PSE Engine Id": "Унесите Гоогле ПСЕ ИД машине", "Enter Image Size (e.g. 512x512)": "Унесите величину слике (нпр. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Унесите кодове језика", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Унесите ознаку модела (нпр. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Унесите број корака (нпр. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Унесите резултат", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Унесите УРЛ адресу Сеарxнг упита", "Enter Seed": "", "Enter Serper API Key": "Унесите Серпер АПИ кључ", "Enter Serply API Key": "", "Enter Serpstack API Key": "Унесите Серпстацк АПИ кључ", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Унесите секвенцу заустављања", "Enter system prompt": "Унеси системски упит", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Унесите Топ К", "Enter URL (e.g. http://127.0.0.1:7860/)": "Унесите адресу (нпр. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Унесите адресу (нпр. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Унесите вашу е-пошту", "Enter Your Full Name": "Унесите ваше име и презиме", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "Унесите вашу лозинку", "Enter your prompt": "", "Enter Your Role": "Унесите вашу улогу", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Грешка", "ERROR": "ГРЕШКА", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Процењивања", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Експериментално", "Explore the cosmos": "", "Export": "Извоз", "Export All Archived Chats": "", "Export All Chats (All Users)": "Извези сва ћаскања (сви корисници)", "Export chat (.json)": "", "Export Chats": "Извези ћаскања", "Export Config to JSON File": "", "Export Functions": "Извези функције", "Export Models": "Извези моделе", "Export Presets": "", "Export Prompts": "Извези упите", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Неуспешно стварање API кључа.", "Failed to read clipboard contents": "Неуспешно читање садржаја оставе", "Failed to save models configuration": "", "Failed to update settings": "", "February": "Фебру<PERSON>р", "Feedback History": "Историјат повратних података", "Feedbacks": "Повратни подаци", "File": "Датотека", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "Режим датотеке", "File not found.": "Датотека није пронађена.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "Датотеке", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Откривено лажно представљање отиска прста: Немогуће је користити иницијале као аватар. Прелазак на подразумевану профилну слику.", "Fluidly stream large external response chunks": "Течно стримујте велике спољне делове одговора", "Focus chat input": "Усредсредите унос ћаскања", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "Фреквентна казна", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "Функције", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "Опште", "General Settings": "Општа подешавања", "Generate Image": "", "Generating search query": "Генерисање упита претраге", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "Добар одговор", "Google Drive": "", "Google PSE API Key": "Гоогле ПСЕ АПИ кључ", "Google PSE Engine Id": "Гоогле ПСЕ ИД мотора", "Group created successfully": "Група направљена успешно", "Group deleted successfully": "Група обрисана успешно", "Group Description": "О<PERSON>ис групе", "Group Name": "Назив групе", "Group updated successfully": "Група измењена успешно", "Groups": "Гру<PERSON>е", "GSA Chat can make mistakes. Review all responses for accuracy.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "нема разговора.", "Hello, {{name}}": "Здраво, {{name}}", "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Сакриј", "Host": "", "How can I help you today?": "Како могу да вам помогнем данас?", "How would you rate this response?": "", "Hybrid Search": "Х<PERSON>б<PERSON><PERSON>дна претрага", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Стварање слика (експериментално)", "Image Generation Engine": "Мотор за стварање слика", "Image Max Compression Size": "", "Image Settings": "Подешавања слике", "Images": "Слике", "Import Chats": "Увези ћаскања", "Import Config from JSON File": "", "Import Functions": "Увези функције", "Import Models": "Увези моделе", "Import Presets": "", "Import Prompts": "Увези упите", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "Укључи `--api` заставицу при покретању stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Инфо", "Input commands": "Унеси наредбе", "Install from Github URL": "Инста<PERSON><PERSON><PERSON><PERSON><PERSON> из Гитхуб УРЛ адресе", "Instant Auto-Send After Voice Transcription": "", "Interface": "Изглед", "Invalid file format.": "", "Invalid Tag": "Неисправна ознака", "is typing...": "", "January": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "придружите се нашем Дискорду за помоћ.", "JSON": "JSON", "JSON Preview": "ЈСОН Преглед", "July": "<PERSON><PERSON>л", "June": "<PERSON><PERSON><PERSON>", "JWT Expiration": "Истек JWT-а", "JWT Token": "JWT жетон", "Kagi Search API Key": "", "Keep Alive": "Одржи трајање", "Key": "", "Keyboard shortcuts": "Пречице на тастатури", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Last Active": "Последња активност", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "Ранг листа", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "Светла", "Listening...": "", "Local": "", "Local Models": "", "Lost": "Пораза", "LTR": "ЛНД", "Made by OpenWebUI Community": "Израдила OpenWebUI заједница", "Make sure to enclose them with": "Уверите се да их затворите са", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Управљање цевоводима", "March": "Ма<PERSON><PERSON>", "Max Tokens (num_predict)": "Маx Токенс (нум_предицт)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Највише 3 модела могу бити преузета истовремено. Покушајте поново касније.", "May": "М<PERSON>ј", "Memories accessible by LLMs will be shown here.": "Памћења које ће бити појављена од овог LLM-а ће бити приказана овде.", "Memory": "Памћење", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Поруке које пошаљете након стварања ваше везе неће бити подељене. Корисници са URL-ом ће моћи да виде дељено ћаскање.", "Min P": "", "Minimum Score": "Најмањи резултат", "Mirostat": "Миростат", "Mirostat Eta": "Мир<PERSON><PERSON><PERSON><PERSON><PERSON> Ета", "Mirostat Tau": "Мирост<PERSON><PERSON> Тау", "MMMM DD, YYYY": "ММММ ДД, ГГГГ", "MMMM DD, YYYY HH:mm": "ММММ ДД, ГГГГ ЧЧ:мм", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "<PERSON>о<PERSON><PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{{modelName}}“ је успешно преузет.", "Model '{{modelTag}}' is already in queue for downloading.": "<PERSON>о<PERSON><PERSON><PERSON> „{{modelTag}}“ је већ у реду за преузимање.", "Model {{modelId}} not found": "Модел {{modelId}} није пронађен", "Model {{modelName}} is not vision capable": "Модел {{моделНаме}} није способан за вид", "Model {{name}} is now {{status}}": "Модел {{наме}} је сада {{статус}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Откривена путања система датотека модела. За ажурирање је потребан кратак назив модела, не може се наставити.", "Model Filtering": "", "Model ID": "ИД модела", "Model IDs": "", "Model Name": "", "Model not selected": "Модел није изабран", "Model Params": "Модел Парамс", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "Садр<PERSON><PERSON><PERSON> модел-датотеке", "Models": "Модели", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "Више", "Name": "Име", "Name your knowledge base": "", "New Chat": "Ново ћаскање", "New folder": "", "New Password": "Нова лозинка", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "Нема резултата", "No search query generated": "Није генерисан упит за претрагу", "No source available": "Нема доступног извора", "No users were found.": "", "No valves to update": "", "None": "Нико", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Напомена: ако подесите најмањи резултат, претрага ће вратити само документе са резултатом већим или једнаким најмањем резултату.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Обавештења", "November": "Новембар", "num_gpu (Ollama)": "", "num_thread (Ollama)": "нум _тхреад (Оллама)", "OAuth ID": "", "October": "Октобар", "Off": "Искључено", "Okay, Let's Go!": "У реду, хајде да кренемо!", "OLED Dark": "OLED тамна", "Ollama": "Ollama", "Ollama API": "Оллама АПИ", "Ollama API disabled": "Оллама АПИ онемогућен", "Ollama API settings updated": "", "Ollama Version": "Издање Ollama-е", "On": "Укључено", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Само алфанумерички знакови и цртице су дозвољени у низу наредби.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Упс! Изгледа да је адреса неважећа. Молимо вас да проверите и покушате поново.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Упс! Користите неподржани метод (само фронтенд). Молимо вас да покренете WebUI са бекенда.", "Open in full screen": "", "Open new chat": "Покрени ново ћаскање", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "Подешавање OpenAI API-ја", "OpenAI API Key is required.": "Потребан је OpenAI API кључ.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "Потребан је OpenAI URL/кључ.", "or": "или", "Organize your users": "Организујте ваше кориснике", "OUTPUT": "", "Output format": "Формат излаза", "Overview": "Преглед", "page": "", "Password": "Лозинка", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF документ (.pdf)", "PDF Extract Images (OCR)": "Извлачење PDF слика (OCR)", "pending": "на чекању", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "Приступ микрофону је одбијен: {{error}}", "Permissions": "", "Personalization": "Прилагођавање", "Pin": "Зака<PERSON>и", "Pinned": "Закачено", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "Цевоводи", "Pipelines Not Detected": "", "Pipelines Valves": "Вентили за цевоводе", "Plain text (.txt)": "Обичан текст (.txt)", "Playground": "Игралиште", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Претходних 30 дана", "Previous 7 days": "Претходних 7 дана", "Profile Image": "Слика профила", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Упит (нпр. „реци ми занимљивост о Римском царству“)", "Prompt Content": "Сад<PERSON><PERSON><PERSON><PERSON> упита", "Prompt created successfully": "", "Prompt suggestions": "Предлози упита", "Prompt updated successfully": "", "Prompts": "Упити", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Повуците \"{{searchValue}}\" са Ollama.com", "Pull a model from Ollama.com": "Повуците модел са Ollama.com", "Query Generation Prompt": "", "Query Params": "Параметри упита", "RAG Template": "RAG шаблон", "Rating": "Оцена", "Re-rank models by topic similarity": "", "Read Aloud": "Прочитај наглас", "Record voice": "Сними глас", "Redirecting you to OpenWebUI Community": "Преусмеравање на OpenWebUI заједницу", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "Регенериши", "Release Notes": "Напомене о издању", "Relevance": "", "Remove": "Уклони", "Remove Model": "Уклони модел", "Rename": "Преим<PERSON><PERSON><PERSON><PERSON>", "Reorder Models": "", "Repeat Last N": "Понови последњих N", "Reply in Thread": "", "Request Mode": "Режим захтева", "Reranking Model": "Модел поновног рангирања", "Reranking model disabled": "Модел поновног рангирања онемогућен", "Reranking model set to \"{{reranking_model}}\"": "Модел поновног рангирања подешен на \"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Улога", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "ДНЛ", "Run": "", "Running": "", "Save": "Сачувај", "Save & Create": "Сачувај и направи", "Save & Update": "Сачувај и ажурирај", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Чување ћаскања директно у складиште вашег прегледача више није подржано. Одвојите тренутак да преузмете и избришете ваша ћаскања кликом на дугме испод. Не брините, можете лако поново увезти ваша ћаскања у бекенд кроз", "Scroll to bottom when switching between branches": "", "Search": "Претра<PERSON>и", "Search a model": "Претражи модел", "Search Base": "", "Search Chats": "Претраж<PERSON> ћаскања", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "Претражи функције", "Search Knowledge": "", "Search Models": "Модели претраге", "Search options": "", "Search Prompts": "Претражи упите", "Search Result Count": "Број резултата претраге", "Search the web": "", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "УРЛ адреса Сеарxнг упита", "See readme.md for instructions": "Погледај readme.md за упутства", "See what's new": "Погледај шта је ново", "Seed": "Семе", "Select a base model": "Избор основног модела", "Select a engine": "Изабери мотор", "Select a function": "Изабери функцију", "Select a group": "Изабери групу", "Select a model": "Изабери модел", "Select a pipeline": "Избор цевовода", "Select a pipeline url": "Избор урл адресе цевовода", "Select a tool": "Изабери алат", "Select Engine": "Изабери мотор", "Select Knowledge": "Изабери знање", "Select model": "Изабери модел", "Select only one model to call": "", "Selected model(s) do not support image inputs": "Изабрани модели не подржавају уносе слика", "Semantic distance to query": "", "Send": "Пошаљи", "Send a message": "", "Send a Message": "Пошаљи поруку", "Send message": "Пошаљи поруку", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Септембар", "Serper API Key": "Серпер АПИ кључ", "Serply API Key": "", "Serpstack API Key": "Серпстацк АПИ кључ", "Server connection verified": "Веза са сервером потврђена", "Set as default": "Подеси као подразумевано", "Set CFG Scale": "", "Set Default Model": "Подеси као подразумевани модел", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Подеси модел уградње (нпр. {{model}})", "Set Image Size": "Подеси величину слике", "Set reranking model (e.g. {{model}})": "Подеси модел поновног рангирања (нпр. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Подеси кораке", "Set Task Model": "Постављање модела задатка", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Подеси глас", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Подешавања", "Settings saved successfully!": "Подешавања успешно сачувана!", "Share": "Подели", "Share Chat": "Подели ћаскање", "Share to OpenWebUI Community": "Подели са OpenWebUI заједницом", "Show": "Прикажи", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "Прикажи пречице", "Show your support!": "", "Sign in": "Пријави се", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Одјави се", "Sign up": "Региструј се", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "Извор", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Грешка у препознавању говора: {{error}}", "Speech-to-Text Engine": "Мотор за говор у текст", "Stop": "", "Stop Sequence": "Секвенца заустављања", "Stream Chat Response": "", "STT Model": "", "STT Settings": "STT подешавања", "Success": "Успех", "Successfully updated.": "Успешно ажурирано.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "Систем", "System Instructions": "", "System Prompt": "Системски упит", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "Температура", "Template": "Шабл<PERSON>н", "Temporary Chat": "Привремено ћаскање", "Text Splitter": "", "Text-to-Speech Engine": "Мотор за текст у говор", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Хвала на вашем коментару!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Резултат треба да буде вредност између 0.0 (0%) и 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "Тема", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Ово осигурава да су ваши вредни разговори безбедно сачувани у вашој бекенд бази података. Хвала вам!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Савет: ажурирајте више променљивих слотова узастопно притиском на тастер Таб у уносу ћаскања након сваке замене.", "Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON>а<PERSON><PERSON><PERSON> (нпр. „реци ми занимљивост“)", "Title Auto-Generation": "Самостално стварање наслова", "Title cannot be an empty string.": "Наслов не може бити празан низ.", "Title Generation Prompt": "Упит за стварање наслова", "TLS": "", "To access the available model names for downloading,": "Да бисте приступили доступним именима модела за преузимање,", "To access the GGUF models available for downloading,": "Да бисте приступили GGUF моделима доступним за преузимање,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Toggle settings": "Пребаци подешавања", "Toggle sidebar": "Пребаци бочну траку", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "<PERSON>ла<PERSON><PERSON>", "Tools Access": "Приступ алатима", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Топ К", "Top P": "Топ П", "Transformers": "", "Trouble accessing Ollama?": "Проблеми са приступом Ollama-и?", "TTS Model": "", "TTS Settings": "TTS подешавања", "TTS Voice": "", "Type": "Тип", "Type Hugging Face Resolve (Download) URL": "Унесите Hugging Face Resolve (Download) адресу", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "Ажу<PERSON><PERSON><PERSON><PERSON><PERSON> и копирај везу", "Update for the latest features and improvements.": "", "Update password": "Ажу<PERSON><PERSON><PERSON><PERSON><PERSON> лозинку", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "Отпреми GGUF модел", "Upload directory": "", "Upload files": "", "Upload Files": "Отпремање датотека", "Upload Pipeline": "", "Upload Progress": "Напредак отпремања", "URL": "", "URL Mode": "Режим адресе", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "Користи Граватар", "Use groups to group your users and assign permissions.": "Користите групе да бисте разврстали ваше кориснике и доделили овлашћења.", "Use Initials": "Користи иницијале", "use_mlock (Ollama)": "усе _млоцк (Оллама)", "use_mmap (Ollama)": "усе _ммап (Оллама)", "user": "корисник", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "Корисници", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "Искористи", "Valid time units:": "Важеће временске јединице:", "Valves": "Вентили", "Valves updated": "Вентили ажурирани", "Valves updated successfully": "Вентили успешно ажурирани", "variable": "променљива", "variable to have them replaced with clipboard content.": "променљива за замену са садржајем оставе.", "Version": "Издање", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "Упозорење", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Упозорење: ако ажурирате или промените ваш модел уградње, мораћете поново да увезете све документе.", "Web": "<PERSON>е<PERSON>", "Web API": "", "Web Loader Settings": "Подешавања веб учитавача", "Web Search": "Wеб претрага", "Web Search Engine": "Wеб претра<PERSON><PERSON><PERSON><PERSON>ч", "Web Search Query Generation": "", "Webhook URL": "Адреса веб-куке", "WebUI Settings": "Подешавања веб интерфејса", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Шта је ново у", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "Победа", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Радни простор", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Напишите предлог упита (нпр. „ко си ти?“)", "Write a summary in 50 words that summarizes [topic or keyword].": "Напишите сажетак у 50 речи који резимира [тему или кључну реч].", "Write something...": "", "Write your model template content here": "", "Yesterday": "Јуче", "You": "Ти", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "Немате архивиране разговоре.", "You have shared this chat": "Поделили сте ово ћаскање", "You're a helpful assistant.": "Ти си користан помоћник.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Youtube Loader Settings": "Подешавања Јутјуб учитавача"}